"""
filer.py

Module to handle interactions with filer.
"""

from __future__ import absolute_import
from builtins import object
import base64
import os
import re
import requests
import time
import urllib3
from deprecated import deprecated
from pathlib import Path
from requests.exceptions import HTTPError
from typing import Optional
from elipy2 import (
    build_metadata_utils,
    code,
    core,
    data,
    filer_paths,
    frostbite_core,
    local_paths,
    LOGGER,
    secrets,
)
from elipy2.exceptions import ELIPYException
from elipy2.frostbite import fbenv_layer
from elipy2.telemetry import collect_metrics


# pylint: disable=too-many-public-methods
# pylint: disable=too-many-lines
# pylint: disable=too-many-statements
class FilerUtils(object):
    """
    Utils class for filer
    """

    def __init__(self):
        if core.use_bilbo():
            self.bilbo = build_metadata_utils.setup_metadata_manager()

    @staticmethod
    @collect_metrics()
    def get_case_sensitive_path(path: str, prefix: str = "\\\\filer.dice.ad.ea.com\\Builds\\"):
        """
        Will Return a UNIX-friendly case-sensitive path
        """

        path_list = list(Path(os.path.normpath(path[len(prefix) :])).parts)
        test_path = os.path.join(prefix)
        while path_list:
            directory = os.listdir(test_path)
            re_string = re.escape(path_list[0])
            found = False
            for i in directory:
                if re.match(rf"^\b(?=\W*){re_string}\b$", i, re.IGNORECASE):
                    test_path = os.path.join(test_path, i)
                    found = True
                    break
            if not found:
                raise ELIPYException(
                    "Failed rebuilding path as folder {} does not exist: {}".format(
                        path_list[0], path
                    )
                )
            path_list.pop(0)
        return test_path

    @staticmethod
    @collect_metrics()
    def _run_delete_with_onefs_api(path: str, old_path: str):
        """
        Will run the actual onefs_api request.

        path - true cased unix sensitive path
        old_path - output, original path structure
        to help debugging if failure occurs.
        """

        start = time.time()

        try:
            cred_dets = secrets.get_secrets({"delete_account": True})
            creds = next(v for v in cred_dets.values())
            usrpass = f"{creds['domain']}\\{creds['username']}:{creds['password']}".encode("utf-8")
        except KeyError as exc:
            raise ELIPYException("Failed getting correct credentials") from exc

        token = base64.b64encode(usrpass)
        token = token.decode("utf-8")
        headers = {"Authorization": f"Basic {token}"}

        urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        url_test = (
            "https://filer.dice.ad.ea.com:8080/" f"namespace/ifs/isilonsthlm/smb/Builds/{path}"
        )

        url = (
            "https://filer.dice.ad.ea.com:8080/"
            f"namespace/ifs/isilonsthlm/smb/Builds/{path}"
            "?recursive=true"
        )

        LOGGER.info("Verifying path validity: {}".format(old_path))
        res_test = requests.request("GET", url_test, headers=headers, verify=False)
        test_results = (
            f"Failed to validate {old_path} with {path}, "
            f"exited with code {res_test.status_code} : [{res_test.text}]"
        )

        if not res_test.ok:
            raise ELIPYException("Path Validation Failed {}".format(test_results))
        else:
            LOGGER.info("Valid Path Created: {}".format(path))

        LOGGER.info("Attempting to delete {}".format(old_path))
        res = requests.request("DELETE", url, headers=headers, verify=False)
        results = (
            f"{old_path} after {time.time() - start} "
            f"seconds with code {res.status_code} : [{res.text}]"
        )

        if not res.ok:
            raise ELIPYException("Failed deleting {}".format(results))
        LOGGER.info("Successfully deleted {}".format(results))
        return res.ok

    @staticmethod
    @collect_metrics()
    def delete_with_onefs_api(path: str, prefix: str = "\\\\filer.dice.ad.ea.com\\Builds\\"):
        """
        DELL OneFS_API method for deletion of builds on DICE filer only!
        https://dl.dell.com/content/docu97296
        """

        should_delete_build = True

        # Prepping Path
        old_path = path
        try:
            path = FilerUtils.get_case_sensitive_path(path)[len(prefix) :]
            new_path = path.replace(os.sep, "/")
        except ELIPYException:
            if not os.path.exists(path):
                LOGGER.warning("Path does not exist {}".format(path))
                bilbo_del = build_metadata_utils.setup_metadata_manager()
                bilbo_del.delete_build(path)
                should_delete_build = False
            else:
                raise ELIPYException("Failed deleting: {}".format(path))

        if should_delete_build:
            FilerUtils._run_delete_with_onefs_api(new_path, old_path)

    @staticmethod
    def deploy_delta_bundles(
        source: str,
        data_branch: str,
        data_changelist: str,
        code_branch: str,
        code_changelist: str,
        platform: str,
        bundles_dir_name: Optional[str] = "bundles",
        combine_build_delta=False,
        combine_data_branch: Optional[str] = None,
        combine_data_changelist: Optional[str] = None,
        combine_code_branch: Optional[str] = None,
        combine_code_changelist: Optional[str] = None,
    ):
        """
        Deploys delta bundles to frosty build base location.
        """
        if (
            combine_data_branch
            and combine_data_changelist
            and combine_code_branch
            and combine_code_changelist
        ):
            # For combined builds, get the combined head bundles path and add "delta" subdirectory
            head_path = filer_paths.get_head_bundles_path(
                data_branch=data_branch,
                data_changelist=data_changelist,
                code_branch=code_branch,
                code_changelist=code_changelist,
                platform=platform,
                bundles_dir_name=bundles_dir_name,
                combine_data_branch=combine_data_branch,
                combine_data_changelist=combine_data_changelist,
                combine_code_branch=combine_code_branch,
                combine_code_changelist=combine_code_changelist,
            )
            delta_path = os.path.join(head_path, "..", "delta")
            delta_path = os.path.normpath(delta_path)
        else:
            # Use original delta bundles path for backward compatibility
            delta_path = filer_paths.get_delta_bundles_path(
                data_branch,
                data_changelist,
                code_branch,
                code_changelist,
                platform,
                bundles_dir_name,
            )

        if os.path.exists(delta_path):
            if not combine_build_delta:
                raise ELIPYException(
                    "Cannot deploy delta bundles to {0}, path already exists!".format(delta_path)
                )
            else:
                LOGGER.warning(
                    "Delta folder {0} already exists, assuming it was created by a previous "
                    "combine patch_frosty job with a different config".format(delta_path)
                )
        else:
            core.robocopy(source, delta_path)

    @staticmethod
    @collect_metrics()
    def deploy_precreated_delta_bundles(
        source: str,
        data_branch: str,
        data_changelist: str,
        code_branch: str,
        code_changelist: str,
        platform: str,
        bundles_dir_name: Optional[str] = "bundles",
        combine_data_branch: Optional[str] = None,
        combine_data_changelist: Optional[str] = None,
        combine_code_branch: Optional[str] = None,
        combine_code_changelist: Optional[str] = None,
    ) -> None:
        """
        Deploys pre-created delta bundles to the delta_bundles_precreated directory structure.
        This creates the directory structure expected by fetch_precreated_delta_bundles().
        """
        # Get the standard delta bundles path and modify it for precreated bundles
        delta_path = filer_paths.get_delta_bundles_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            bundles_dir_name=bundles_dir_name,
            combine_data_branch=combine_data_branch,
            combine_data_changelist=combine_data_changelist,
            combine_code_branch=combine_code_branch,
            combine_code_changelist=combine_code_changelist,
        )

        if "\\delta" in delta_path:
            precreated_path = delta_path.replace("\\delta", "\\delta_bundles_precreated")

        if os.path.exists(precreated_path):
            raise ELIPYException(
                "Cannot deploy pre-created delta bundles to {0}, path already exists!".format(precreated_path)
            )

        core.robocopy(source, precreated_path)

    @staticmethod
    @collect_metrics()
    def deploy_head_bundles(
        source: str,
        data_branch: str,
        data_changelist: str,
        code_branch: str,
        code_changelist: str,
        platform: str,
        bundles_dir_name: Optional[str] = "bundles",
    ) -> None:
        """
        Deploys head bundles to frosty build base location.
        """
        head_path = filer_paths.get_head_bundles_path(
            data_branch, data_changelist, code_branch, code_changelist, platform, bundles_dir_name
        )
        if os.path.exists(head_path):
            raise ELIPYException(
                "Cannot deploy head bundles to {0}, path already exists!".format(head_path)
            )
        core.robocopy(source, head_path)

    @staticmethod
    @collect_metrics()
    def deploy_combined_head_bundles(
        source: str,
        data_branch: str,
        data_changelist: str,
        code_branch: str,
        code_changelist: str,
        platform: str,
        combine_data_branch: str,
        combine_data_changelist: str,
        combine_code_branch: str,
        combine_code_changelist: str,
        bundles_dir_name: Optional[str] = "bundles",
    ) -> None:
        """
        Deploys combined head bundles to the network path that
        includes combined streams and changelists in the path.
        """
        head_path = filer_paths.get_head_bundles_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            bundles_dir_name=bundles_dir_name,
            combine_data_branch=combine_data_branch,
            combine_data_changelist=combine_data_changelist,
            combine_code_branch=combine_code_branch,
            combine_code_changelist=combine_code_changelist,
        )
        if os.path.exists(head_path):
            raise ELIPYException(
                "Cannot deploy combined head bundles to {0}, path already exists!".format(head_path)
            )
        core.robocopy(source, head_path)

    @staticmethod
    @collect_metrics()
    def deploy_avalanche_combine_output(
        source: str,
        data_branch: str,
        data_changelist: str,
        code_branch: str,
        code_changelist: str,
        platform: str,
        output_dir_name: Optional[str] = "combine_output",
        combine_data_branch: Optional[str] = None,
        combine_data_changelist: Optional[str] = None,
        combine_code_branch: Optional[str] = None,
        combine_code_changelist: Optional[str] = None,
    ) -> None:
        """
        Deploys avalanche combine output to frosty build base location.
        """
        if (
            combine_data_branch
            and combine_data_changelist
            and combine_code_branch
            and combine_code_changelist
        ):
            # Deploy to combined bundles path structure
            output_path = filer_paths.get_head_bundles_path(
                data_branch=data_branch,
                data_changelist=data_changelist,
                code_branch=code_branch,
                code_changelist=code_changelist,
                platform=platform,
                bundles_dir_name=output_dir_name,
                combine_data_branch=combine_data_branch,
                combine_data_changelist=combine_data_changelist,
                combine_code_branch=combine_code_branch,
                combine_code_changelist=combine_code_changelist,
            )
        else:
            # Use original path structure for backward compatibility
            output_path = filer_paths.get_bundles_path(
                data_branch,
                data_changelist,
                code_branch,
                code_changelist,
                platform,
                output_dir_name,
            )

        if os.path.exists(output_path):
            LOGGER.info(
                "Cannot deploy avalanche combine output to {0}, path already exists!".format(
                    output_path
                )
            )
        else:
            core.robocopy(source, output_path)

    @staticmethod
    @collect_metrics()
    def deploy_state(
        source: str,
        data_branch: str,
        data_changelist: str,
        code_branch: str,
        code_changelist: str,
        platform: str,
        bundles_dir_name: Optional[str] = "bundles",
    ):
        """
        Deploys state to frosty build base location.
        """
        state_path = filer_paths.get_state_path(
            data_branch, data_changelist, code_branch, code_changelist, platform, bundles_dir_name
        )

        if os.path.exists(state_path):
            raise ELIPYException(
                "Cannot deploy state to {0}, path already exists!".format(state_path)
            )

        core.robocopy(source, state_path)

    @staticmethod
    @collect_metrics()
    def fetch_delta_bundles(
        data_branch=None,
        data_changelist=None,
        code_branch=None,
        code_changelist=None,
        platform=None,
        dest=None,
    ):
        """
        Fetches delta bundles from frosty build base location.
        """

        delta_path = filer_paths.get_delta_bundles_path(
            data_branch, data_changelist, code_branch, code_changelist, platform
        )

        if dest is None:
            raise ELIPYException("Cannot fetch delta bundles without a destination.")

        core.robocopy(delta_path, dest, purge=True)

    @staticmethod
    @collect_metrics()
    def fetch_head_bundles(
        data_branch: str,
        data_changelist: str,
        code_branch: str,
        code_changelist: str,
        platform: str,
        dest: str,
        bundles_dir_name: Optional[str] = "bundles",
    ):
        """
        Fetches head bundles from frosty build base location.
        """

        head_path = filer_paths.get_head_bundles_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            bundles_dir_name=bundles_dir_name,
        )

        core.robocopy(head_path, dest, purge=True)

    @staticmethod
    @collect_metrics()
    def fetch_combined_bundles(
        data_branch: str,
        data_changelist: str,
        code_branch: str,
        code_changelist: str,
        platform: str,
        dest: str,
        combine_data_branch: str,
        combine_data_changelist: str,
        combine_code_branch: str,
        combine_code_changelist: str,
        bundles_dir_name: Optional[str] = "bundles",
    ):
        """
        Fetches pre-created combined bundles from network share.
        """
        # Get the network path for combined bundles using updated get_head_bundles_path
        combined_path = filer_paths.get_head_bundles_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            bundles_dir_name=bundles_dir_name,
            combine_data_branch=combine_data_branch,
            combine_data_changelist=combine_data_changelist,
            combine_code_branch=combine_code_branch,
            combine_code_changelist=combine_code_changelist,
        )

        # Copy the combined bundles from network share to destination
        core.robocopy(combined_path, dest, purge=True)

    @staticmethod
    @collect_metrics()
    def fetch_precreated_delta_bundles(
        data_branch: str,
        data_changelist: str,
        code_branch: str,
        code_changelist: str,
        platform: str,
        dest: str,
        combine_data_branch: Optional[str] = None,
        combine_data_changelist: Optional[str] = None,
        combine_code_branch: Optional[str] = None,
        combine_code_changelist: Optional[str] = None,
    ):
        """
        Fetches pre-created delta bundles from network share.
        """
        # Get the standard delta bundles path and modify it for precreated bundles
        delta_path = filer_paths.get_delta_bundles_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            combine_data_branch=combine_data_branch,
            combine_data_changelist=combine_data_changelist,
            combine_code_branch=combine_code_branch,
            combine_code_changelist=combine_code_changelist,
        )

        # For combined builds, the path ends with /delta, so we need to replace it correctly
        # Handle both Windows and Unix path separators
        if "\\delta" in delta_path:
            delta_path = delta_path.replace("\\delta", "\\delta_bundles_precreated")
        else:
            delta_path = delta_path.replace("/delta", "/delta_bundles_precreated")

        if dest is None:
            raise ELIPYException("Cannot fetch pre-created delta bundles without a destination.")

        core.robocopy(delta_path, dest, purge=True)

    @staticmethod
    @collect_metrics()
    def fetch_baseline_state(
        code_branch: str,
        code_changelist: str,
        data_branch: str,
        data_changelist: str,
        platform: str,
        dest: Optional[str] = None,
        bundles_dir_name: Optional[str] = "bundles",
    ):
        """
        Copies the Avalanche state deployed from $branch $changelist to $dest
        """
        baseline_path = filer_paths.get_baseline_state_path(
            data_branch, data_changelist, code_branch, code_changelist, platform, bundles_dir_name
        )
        if dest is None:
            dest = os.path.join(frostbite_core.get_tnt_root(), "local", "baseline_state", platform)
        LOGGER.info("Copying baseline state from {0} to {1}".format(baseline_path, dest))
        core.robocopy(baseline_path, dest, purge=True)

    @staticmethod
    @collect_metrics()
    def fetch_baseline_bundles(
        code_branch: str,
        code_changelist: str,
        data_branch: str,
        data_changelist: str,
        platform: str,
        dest: Optional[str] = None,
        bundles_dir_name: Optional[str] = "bundles",
    ):
        """
        Copies the delta bundles deployed from $branch $changelist to $dest
        """
        baseline_path = filer_paths.get_baseline_delta_bundles_path(
            data_branch, data_changelist, code_branch, code_changelist, platform, bundles_dir_name
        )
        if dest is None:
            dest = os.path.join(frostbite_core.get_tnt_root(), "local", bundles_dir_name, "base")
        LOGGER.info("Copying baseline delta bundles from {0} to {1}".format(baseline_path, dest))
        core.robocopy(baseline_path, dest, purge=True)

    @staticmethod
    @collect_metrics()
    def fetch_baseline_bundles_head(
        code_branch: str,
        code_changelist: str,
        data_branch: str,
        data_changelist: str,
        platform: str,
        dest: Optional[str] = None,
        bundles_dir_name: Optional[str] = "bundles",
    ):
        """
        Copies the head bundles deployed from $branch $changelist to $dest
        """
        baseline_path = filer_paths.get_baseline_head_bundles_path(
            data_branch, data_changelist, code_branch, code_changelist, platform, bundles_dir_name
        )
        if dest is None:
            dest = os.path.join(frostbite_core.get_tnt_root(), "local", bundles_dir_name, "base")
        LOGGER.info("Copying baseline head bundles from {0} to {1}".format(baseline_path, dest))
        core.robocopy(baseline_path, dest, purge=True)

    @staticmethod
    @collect_metrics()
    def fetch_baseline_ps_package(
        data_branch: str,
        data_changelist: str,
        code_branch: str,
        code_changelist: str,
        region: str,
        destination: str,
        platform: str,
        package_type: Optional[str] = "patch",
        config: Optional[str] = "retail",
        combine_data_branch: Optional[str] = None,
        combine_data_changelist: Optional[str] = None,
        combine_code_branch: Optional[str] = None,
        combine_code_changelist: Optional[str] = None,
    ):
        """
        Fetches the PS4/PS5 baseline package.
        The baseline package is required as input to patch builds, for example.
        """
        baseline_build_path = filer_paths.get_baseline_build_path(
            data_branch, data_changelist, code_branch, code_changelist
        )

        baseline_package_location = os.path.join(
            baseline_build_path, platform.lower(), package_type, region, config
        )

        if combine_code_changelist and combine_data_changelist:
            baseline_package_location = os.path.join(
                baseline_package_location,
                combine_data_branch,
                str(combine_data_changelist),
                combine_code_branch,
                str(combine_code_changelist),
            )

        LOGGER.info(
            "Copying {0} baseline from {1} to {2}".format(
                platform, baseline_package_location, destination
            )
        )

        core.robocopy(baseline_package_location, destination, extra_args=["/XF", "*.iso", "/MIR"])

    @staticmethod
    @collect_metrics()
    @deprecated(version="6.3", reason="You should use fetch_baseline_ps_package function")
    def fetch_baseline_ps4_package(
        data_branch=None,
        data_changelist=None,
        code_branch=None,
        code_changelist=None,
        package_type="patch",
        config="retail",
        region=None,
        destination=None,
    ):
        """
        Fetches the PS4 baseline package.
        The PS4 baseline package is required as input to patch builds, for example.
        """

        FilerUtils.fetch_baseline_ps_package(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            package_type=package_type,
            config=config,
            region=region,
            destination=destination,
            platform="ps4",
        )

    @staticmethod
    @collect_metrics()
    def fetch_baseline_xb1_layout(
        data_branch=None,
        data_changelist=None,
        code_branch=None,
        code_changelist=None,
        package_type="patch",
        config=None,
        region="ww",
        destination=None,
    ):
        """
        Fetches the layout.xml file from a baseline XB1 build.

        The layout.xml file is required as input to a patch build.
        """

        if destination is None:
            raise ELIPYException("Unable to fetch baseline XB1 layout without destination.")

        baseline_build_path = filer_paths.get_baseline_build_path(
            data_branch, data_changelist, code_branch, code_changelist
        )

        baseline_package_location = os.path.join(
            baseline_build_path, "xb1", package_type, region, config
        )

        LOGGER.info(
            "Copying XB1 layout.xml from {0} to {1}".format(baseline_package_location, destination)
        )

        core.robocopy(baseline_package_location, destination, extra_args=["layout.xml", "/MIR"])

    @staticmethod
    @collect_metrics()
    def fetch_baseline_xb_priorpackage(
        data_branch: str,
        data_changelist: str,
        code_branch: str,
        code_changelist: str,
        config: str,
        destination: str,
        platform: str,
        package_type: Optional[str] = "patch",
        region: Optional[str] = "ww",
        combine_data_branch: Optional[str] = None,
        combine_data_changelist: Optional[str] = None,
        combine_code_branch: Optional[str] = None,
        combine_code_changelist: Optional[str] = None,
    ):
        """
        Fetches the xvc & phd file from the baseline XB1/XBSX build.
        The xvc file is required as input to priorpackage argument for CUv3 (Content Update v3).
        """

        if destination is None:
            raise ELIPYException("Unable to fetch baseline XB XVC without destination.")

        baseline_build_path = filer_paths.get_baseline_build_path(
            data_branch, data_changelist, code_branch, code_changelist
        )

        baseline_package_location = os.path.join(
            baseline_build_path, platform.lower(), package_type, region, config
        )

        if combine_code_changelist and combine_data_changelist:
            baseline_package_location = os.path.join(
                baseline_package_location,
                combine_data_branch,
                str(combine_data_changelist),
                combine_code_branch,
                str(combine_code_changelist),
            )

        LOGGER.info(
            "Copying XB priorpackage files from {0} to {1}".format(
                baseline_package_location, destination
            )
        )

        if platform.lower() == "xb1":
            if frostbite_core.minimum_fb_version(year=2022, season=1, version_nr=2):
                core.robocopy(
                    baseline_package_location,
                    destination,
                    purge=True,
                    extra_args=[
                        "*q5ha1ztykcgvj_x.xvc",
                        "*q5ha1ztykcgvj_x.phd",
                        "chunkmanifest.txt",
                        "layout*.xml",
                    ],
                )

                for file in os.listdir(destination):
                    if file.endswith("q5ha1ztykcgvj_x.xvc"):  # pragma: no cover
                        return os.path.join(destination, file)
            else:
                core.robocopy(
                    baseline_package_location,
                    destination,
                    purge=True,
                    extra_args=[
                        "*q5ha1ztykcgvj",
                        "*q5ha1ztykcgvj.phd",
                        "*zwks512sysnyr",
                        "*zwks512sysnyr.phd",
                        "chunkmanifest.txt",
                        "layout*.xml",
                    ],
                )

                for file in os.listdir(destination):
                    if file.endswith("zwks512sysnyr") or file.endswith(
                        "q5ha1ztykcgvj"
                    ):  # pragma: no cover
                        return os.path.join(destination, file)

        elif platform.lower() == "xbsx":
            core.robocopy(
                baseline_package_location,
                destination,
                purge=True,
                extra_args=[
                    "*q5ha1ztykcgvj_xs.xvc",
                    "*q5ha1ztykcgvj_xs.phd",
                    "chunkmanifest.txt",
                    "layout*.xml",
                ],
            )

            for file in os.listdir(destination):
                if file.endswith("q5ha1ztykcgvj_xs.xvc"):  # pragma: no cover
                    return os.path.join(destination, file)

        raise ELIPYException("File required for priorpackage (CUv3) has not been copied correctly")

    @staticmethod
    @collect_metrics()
    def fetch_baseline_win64_chunkmanifest(
        data_branch: str,
        data_changelist: str,
        code_branch: str,
        code_changelist: str,
        config: str,
        package_type: str = "patch",
        region: str = "ww",
        destination: Optional[str] = None,
        combine_data_branch: Optional[str] = None,
        combine_data_changelist: Optional[str] = None,
        combine_code_branch: Optional[str] = None,
        combine_code_changelist: Optional[str] = None,
    ):
        """
        Fetches the chunkmanifest.txt file from a baseline Win64 build.

        The chunkmanifest.txt file is required as input to a patch build.
        """

        if destination is None:
            raise ELIPYException(
                "Unable to fetch baseline Win64 chunkmanifest without destination."
            )

        baseline_build_path = filer_paths.get_baseline_build_path(
            data_branch, data_changelist, code_branch, code_changelist
        )

        baseline_package_location = os.path.join(
            baseline_build_path, "win64", package_type, region, config
        )

        if combine_code_changelist and combine_data_changelist:
            baseline_package_location = os.path.join(
                baseline_package_location,
                combine_data_branch,
                str(combine_data_changelist),
                combine_code_branch,
                str(combine_code_changelist),
            )

        LOGGER.info(
            "Copying Win64 chunkmanifest.txt from {0} to {1}".format(
                baseline_package_location, destination
            )
        )

        core.robocopy(
            baseline_package_location, destination, extra_args=["chunkmanifest.txt", "/MIR"]
        )

    def deploy_tnt_local_build(
        self, branch, changelist, platform, config, nomaster=False, use_zip=True
    ):
        """
        Deploys the content of TnT/Local excluding Bin to filer.
        The theory is this can be used to quickly bootstrap a "cold" machine to have a cache,
        thus allowing an incremental build to be performed on it even though it's never built.

        We exclude Bin since that's the binary output which is already deployed in deploy_code.
        """
        tnt_local = local_paths.get_tnt_local_path()
        tnt_local_build = os.path.join(tnt_local, "Build")
        dest = filer_paths.get_tnt_local_build_path(
            branch=branch,
            changelist=changelist,
            platform=platform,
            config=config,
            nomaster=nomaster,
        )

        LOGGER.info("Deploying contents of {0} to {1}, excluding Bin.".format(tnt_local, dest))

        if core.use_bilbo():
            self.bilbo.register_tnt_local_build(
                dest, changelist=changelist, branch=branch, platform=platform, config=config
            )

        if nomaster:
            platform = platform + "_nomaster"

        if use_zip:
            local_zip_file = os.path.join(tnt_local, "state.zip")

            extra_args = ["-r", os.path.join(tnt_local, "*_*_*.sln"), ">", "nul"]

            try:
                core.create_zip(
                    source=tnt_local_build,
                    destination=local_zip_file,
                    additional_args=extra_args,
                    compression=0,
                    keep_dir_source=True,
                )
                core.robocopy(tnt_local, dest, extra_args=["state.zip", "/s"])
                os.remove(local_zip_file)
            except Exception as exp:
                LOGGER.warning("Failed to deploy tnt_local, continuing. {}".format(exp))
        else:
            extra_args = [
                "/XD",
                "Bin",
                "Frosty",
                "baseline_state",
                "current_delta",
                "ps4_live_package",
                "ps4_disk_package",
            ]

            try:
                core.robocopy(tnt_local, dest, extra_args=extra_args, quiet=True)
            except Exception as exp:
                LOGGER.warning("Failed to deploy tnt_local, continuing. {}".format(exp))

    @collect_metrics()
    def import_tnt_local_build(
        self,
        branch,
        changelist=None,
        platform=None,
        config=None,
        nomaster=False,
        close_handles=True,
    ):
        """
        Imports the content of TnT/Local from filer.
        """
        try:
            if changelist is None and core.use_bilbo():
                if nomaster:
                    plt = platform + "_nomaster"
                else:
                    plt = platform
                try:
                    build = self.bilbo.get_last_successful(
                        build_type="tnt_local", branch=branch, platform=plt, config=config
                    )
                # Can happen if Bilbo index doesn't exist (i.e. new project).
                except HTTPError:
                    build = dict()
                if "changelist" in build:
                    changelist = build["changelist"]
                else:
                    raise ELIPYException(
                        f"No changelist found for tnt_local build on bilbo for {branch}."
                    )

            dest = local_paths.get_tnt_local_path()
            source = filer_paths.get_tnt_local_build_path(
                branch=branch,
                changelist=changelist,
                platform=platform,
                config=config,
                nomaster=nomaster,
            )

            if os.path.isfile(os.path.join(source, "state.zip")):
                code.CodeUtils.clean_local(close_handles=close_handles)
                core.robocopy(source, dest, purge=True, extra_args=["state.zip"])
                local_zip_path = os.path.join(dest, "state.zip")
                core.extract_zip(source=local_zip_path, destination=dest)
            elif os.path.isdir(source):
                LOGGER.info("Importing contents of {0} to {1}.".format(source, dest))
                if core.use_bilbo():
                    self.bilbo.register_as_used(source)
                core.robocopy(source, dest, purge=True, quiet=True)
            else:
                raise ELIPYException(f"Path {source} does not exist.")
            return changelist
        except Exception as exp:
            LOGGER.warning(
                (
                    "Import of tnt_local failed with\n{}\nWill do a clean_local as a safety "
                    "measure and continue."
                ).format(exp)
            )
            code.CodeUtils.clean_local(close_handles=close_handles)
            return ""

    @collect_metrics()
    def deploy_avalanche_state(
        self, platform, branch, data_changelist, code_changelist, remote_host=[]
    ):
        """
        Deploys Avalanche state to filer
        """
        # export avalanche state to local folder via avalanchecli
        source = local_paths.get_local_avalanche_export_path(
            branch, data_changelist, code_changelist, platform
        )

        dest = filer_paths.get_avalanche_export_path(
            branch, platform, data_changelist, code_changelist
        )

        core.robocopy(source, dest, quiet=True)
        if core.use_bilbo():
            self.bilbo.register_avalanche_state(
                path=dest,
                data_changelist=data_changelist,
                code_changelist=code_changelist,
                branch=branch,
                platform=platform,
                remote_host=remote_host,
            )

    @collect_metrics()
    def fetch_avalanche_state(self, platform, branch, data_changelist, code_changelist):
        """
        Copies an exported avalanche state from filer to local and imports it
        """
        # Copy state from network share
        local_path = local_paths.get_local_avalanche_export_path(
            branch, data_changelist, code_changelist, platform
        )
        source_path = filer_paths.get_avalanche_export_path(
            branch, platform, data_changelist, code_changelist
        )

        if os.path.exists(source_path):
            core.robocopy(source_path, local_path, quiet=True)
            if core.use_bilbo():
                self.bilbo.register_as_used(source_path)
        else:
            raise ELIPYException("No previous Avalanche state to import. Skipping import process.")

    @collect_metrics()
    def deploy_ant_local_build(self, branch, changelist):
        """
        Deploys the content of ANT/Local cache to filer.
        The theory is this can be used to quickly bootstrap a "cold" machine to have a cache,
        thus allowing an incremental build to be performed on it even though it's never built.
        """
        dest = filer_paths.get_ant_local_build_path(branch, changelist)
        source = local_paths.get_ant_local()

        extra_args = ["/XD", "Assets", "Raw", "Resources", "Type"]
        LOGGER.info("Deploying contents of {0} to {1}.".format(source, dest))
        try:
            core.robocopy(source, dest, extra_args=extra_args, quiet=True)
            if core.use_bilbo():
                self.bilbo.register_ant_local_build(dest, changelist=changelist, branch=branch)
        except Exception as exp:
            LOGGER.warning("Failed to deploy ant_local, continuing. {}".format(exp))

    @collect_metrics()
    def import_ant_local_build(self, branch, changelist=None, platform=None):
        """
        Imports the content of ANT/Local from filer.
        """
        dest = local_paths.get_ant_local()
        if changelist is None and platform and core.use_bilbo():
            build = self.bilbo.get_last_successful(build_type="ant_local", branch=branch)
            if "changelist" in build:
                changelist = build["changelist"]
            else:
                LOGGER.warning(
                    "Could not find ant_local build for branch {} in bilbo".format(branch)
                )
                LOGGER.warning("Skipping import of ANT Local")
                data.DataUtils.clean_ant_local()
                return
        source = filer_paths.get_ant_local_build_path(branch=branch, changelist=changelist)
        if not os.path.isdir(source):
            LOGGER.warning("Path {0} does not exist. Skipping import of ANT Local".format(source))
            data.DataUtils.clean_ant_local()
        else:
            LOGGER.info("Importing contents of {0} to {1}.".format(source, dest))
            core.robocopy(source, dest, purge=True, quiet=True)

    @collect_metrics()
    def deploy_code(
        self,
        branch,
        changelist,
        platform,
        config,
        artifact_name=None,
        source=None,
        deploy_tnt=False,
        deploy_tnt_local_build=False,
        nomaster=False,
        use_fbenv_copy=None,
        fbenv_copy_args=[],
        overwrite=False,
        use_state_zip=False,
        deploy_houdini=False,
        deploy_tests=False,
        deploy_frostedtests=False,
        tool_targets=["pipeline", "frosted", "win64-dll"],
        custom_tag=None,
        mirror=True,
        custom_binaries_destination=None,
        skip_bilbo=False,
    ):
        """
        Deploy code build to filer
        """

        if deploy_tnt:
            LOGGER.warning("Deploying the full TnT folder is no longer supported.")

        if artifact_name:
            artifacts = [artifact_name]
        else:
            artifacts = fbenv_layer.get_enabled_licensees()
        if len(artifacts) == 0:
            artifacts = [fbenv_layer.get_exe_name()]

        root_dest = filer_paths.get_code_build_root_path(
            branch, changelist, custom_tag=custom_tag, nomaster=nomaster
        )
        if use_fbenv_copy is None:
            use_fbenv_copy = frostbite_core.minimum_fb_version(year=2019, version_nr=1)

        if platform and platform.lower() == "tool":
            test_platform = "win64"
            platforms = tool_targets
            if (
                "frosted" in platforms
                and deploy_frostedtests
                and (use_fbenv_copy or fbenv_layer.use_fbcli())
            ):  # We don't have support for frostedtests in the old copy method.
                platforms.append("frostedtests")
        else:
            test_platform = platform
            platforms = [platform]

        if use_fbenv_copy:
            full_dest = filer_paths.get_code_build_path(
                branch, changelist, platform, config, custom_tag=custom_tag, nomaster=nomaster
            )
            if os.path.exists(full_dest) and not overwrite and platform.lower() != "tool":
                raise ELIPYException(
                    "Attempting to deploy to a path that already exists.\
                    Possibly because a previous build succeeded in deploying before failing.\
                    This can cause us to lose binaries and symbols and is not allowed."
                )
            remote_dir = os.path.dirname(root_dest) if fbenv_layer.use_fbcli() else root_dest
            shared_args = {
                "remote_dir": (
                    custom_binaries_destination if custom_binaries_destination else remote_dir
                ),
                "copy_build_args": fbenv_copy_args,
                "label": changelist,
            }
            try:
                _mirror = mirror
                for artifact in artifacts:
                    # consecutive artifacts shouldn't set mirror to true to avoid overwrites
                    fbenv_layer.pushfrostbitebuild(
                        artifact=artifact,
                        platforms=platforms,
                        variant=config,
                        mirror=_mirror,
                        **shared_args,
                    )
                    _mirror = False
                if deploy_tests:
                    fbenv_layer.pushfrostbitebuild(
                        artifact="tests", platforms=[test_platform], variant=config, **shared_args
                    )
            except Exception as exc:
                LOGGER.warning("EA-copy failed with: {}".format(exc))
                LOGGER.info("Failed to deploy files, will try again.")
                time.sleep(30)
                _mirror = mirror
                for artifact in artifacts:
                    fbenv_layer.pushfrostbitebuild(
                        artifact=artifact,
                        platforms=platforms,
                        variant=config,
                        mirror=_mirror,
                        **shared_args,
                    )
                    _mirror = False
                if deploy_tests:
                    fbenv_layer.pushfrostbitebuild(
                        artifact="tests", platforms=[test_platform], variant=config, **shared_args
                    )
        else:
            for _platform in platforms:
                if source is None:
                    _source = local_paths.get_local_build_path(_platform, config)
                else:
                    _source = source
                dest = filer_paths.get_code_build_path(
                    branch,
                    changelist,
                    _platform,
                    config,
                    custom_tag=custom_tag,
                    nomaster=nomaster,
                )
                LOGGER.info("Deploy code artifacts {0} to {1}".format(_source, dest))

                if os.path.exists(dest) and not overwrite:
                    raise ELIPYException(
                        "Attempting to deploy to a path that already exists.\
                        Possibly because a previous build succeeded in deploying before failing.\
                        This can cause us to lose binaries and symbols and is not allowed."
                    )

                core.robocopy(_source, dest)

        if core.use_bilbo() and skip_bilbo is False:
            self.bilbo.register_code_build(root_dest, changelist=changelist, branch=branch)

        self._deploy_additional_artifacts(
            root_dest,
            deploy_houdini,
            deploy_tnt_local_build,
            branch,
            changelist,
            platform,
            config,
            nomaster,
            use_state_zip,
        )

    def _deploy_additional_artifacts(
        self,
        root_dest,
        deploy_houdini,
        deploy_tnt_local_build,
        branch,
        changelist,
        platform,
        config,
        nomaster,
        use_state_zip,
    ):
        """
        deploy additional artifacts
        """
        if deploy_houdini:
            LOGGER.info("Deploying Houdini files to {0}".format(root_dest))
            houdini_source = os.path.join(frostbite_core.get_tnt_root(), "bin", "procedural")
            core.robocopy(houdini_source, os.path.join(root_dest, "procedural"))

        if deploy_tnt_local_build:
            self.deploy_tnt_local_build(
                branch, changelist, platform, config, nomaster=nomaster, use_zip=use_state_zip
            )

    @collect_metrics()
    def fetch_code(
        self,
        branch,
        changelist,
        platform,
        config,
        artifact_name=None,
        mirror=True,
        dest=None,
        purge=True,
        nomaster=False,
        use_fbenv_copy=None,
        exclude_pdb_files=None,
        fbenv_copy_args=[],
        fetch_tests=False,
        use_bilbo=True,
        custom_tag=None,
        target_build_share=None,
    ):
        """
        Fetch code build from filer

        use_fbenv_copy will use the fbenv pullfrostbitebuild function introduced in fb19

        Default for exclude_pdb_files is to exclude pdbs for platform tool and pipeline,
        this can be overridden by setting the variable to true or false
        """
        LOGGER.start_group("Fetch code build from filer")
        if artifact_name:
            artifacts = [artifact_name]
        else:
            artifacts = fbenv_layer.get_enabled_licensees()
        if len(artifacts) == 0:
            artifacts = [fbenv_layer.get_exe_name()]

        if use_fbenv_copy is None:
            use_fbenv_copy = frostbite_core.minimum_fb_version(year=2019, version_nr=1)

        # Don't translate 'tool' platform to ["pipeline", "frosted", "win64-dll"] on FB2019-PR1
        if platform and platform.lower() == "tool" and not use_fbenv_copy:
            platforms = ["pipeline", "frosted", "win64-dll"]
            test_platform = "win64"
        else:
            platforms = [platform]
            test_platform = platform

        if (
            exclude_pdb_files is None
            and platform.lower() in ["tool", "pipeline"]
            or exclude_pdb_files
        ):
            extra_args = ["/XF", "*.pdb"]
        else:
            extra_args = []

        if use_fbenv_copy:
            remote_dir = filer_paths.get_code_build_root_path(
                branch,
                changelist,
                custom_tag=custom_tag,
                nomaster=nomaster,
                target_build_share=target_build_share,
            )
            if fbenv_layer.use_fbcli():
                # fb pullbuild/pushbuild has a mandatory "changelist" field
                remote_dir = os.path.dirname(remote_dir)
            _mirror = mirror
            for artifact in artifacts:
                fbenv_layer.pullfrostbitebuild(
                    artifact=artifact,
                    platforms=platforms,
                    variant=config,
                    remote_dir=remote_dir,
                    mirror=_mirror,
                    label=changelist,
                    copy_build_args=fbenv_copy_args,
                )
                _mirror = False
            if fetch_tests:
                fbenv_layer.pullfrostbitebuild(
                    artifact="tests",
                    platforms=[test_platform],
                    variant=config,
                    remote_dir=remote_dir,
                    label=changelist,
                    copy_build_args=fbenv_copy_args,
                )
        else:
            for _platform in platforms:
                source = filer_paths.get_code_build_path(
                    branch,
                    changelist,
                    _platform,
                    config,
                    custom_tag=custom_tag,
                    nomaster=nomaster,
                    target_build_share=target_build_share,
                )
                _dest = dest
                if dest is None:
                    _dest = local_paths.get_local_build_path(platform=_platform, config=config)
                core.robocopy(source, _dest, purge=purge, extra_args=extra_args)

        for _platform in platforms:
            if use_bilbo:
                if core.use_bilbo():
                    self.bilbo.register_as_used(
                        filer_paths.get_code_build_root_path(branch, changelist, nomaster=nomaster)
                    )
        LOGGER.end_group()

    @collect_metrics()
    def create_combine_stream_info_file(
        self,
        combine_data_branch,
        combine_data_changelist,
    ):
        """
        Create a <secondary combine stream name>_CL_<changelist>.txt file.
        For builds produced using avalanche combine.
        """
        source_file = f"{combine_data_branch}_CL_{combine_data_changelist}.txt"
        Path(source_file).touch()
        source_path = Path(source_file).resolve()
        source_dir = str(source_path.parent)

        dest_dir = local_paths.get_local_frosty_path()
        dest_path = os.path.join(dest_dir, source_file)
        if os.path.exists(dest_path):
            raise ELIPYException(f"Cannot deploy {source_file} to {dest_dir}, file already exists!")

        core.robocopy(
            source_dir,
            str(dest_dir),
            single_file=True,
            extra_args=[source_file],
        )

    @collect_metrics()
    def deploy_frosty_build(
        self,
        data_changelist=None,
        data_branch=None,
        code_changelist=None,
        code_branch=None,
        platform=None,
        package_type=None,
        config=None,
        additional_configs=None,
        dataset=None,
        source=None,
        region="WW",
        combine_data_changelist=None,
        combine_data_branch=None,
        combine_code_changelist=None,
        combine_code_branch=None,
        content_layer=None,
    ):
        """
        Deploy Frosty build.
        """
        if source is None:
            source = local_paths.get_local_frosty_path()

        dest = filer_paths.get_frosty_build_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            package_type=package_type,
            region=region,
            config=config,
            combine_data_branch=combine_data_branch,
            combine_data_changelist=combine_data_changelist,
            combine_code_branch=combine_code_branch,
            combine_code_changelist=combine_code_changelist,
            content_layer=content_layer,
        )
        LOGGER.info("Deploy packaged build from {0} to {1}".format(source, dest))

        if os.path.exists(dest):
            raise ELIPYException(
                "Attempting to deploy to a path that already exists.\
                Possibly because a previous build succeeded in deploying before failing.\
                This can cause us to lose binaries and symbols and is not allowed."
            )

        core.robocopy(source, dest, include_empty_dirs=False)

        if core.use_bilbo():
            self.bilbo.register_frosty_build(
                dest,
                data_changelist=data_changelist,
                data_branch=data_branch,
                code_changelist=code_changelist,
                code_branch=code_branch,
                platform=platform,
                package_type=package_type,
                region=region,
                config=config,
                additional_configs=additional_configs,
                dataset=dataset,
                combine_data_changelist=combine_data_changelist,
                combine_data_branch=combine_data_branch,
                combine_code_changelist=combine_code_changelist,
                combine_code_branch=combine_code_branch,
            )

    @staticmethod
    @collect_metrics()
    def fetch_frosty_build(
        data_changelist=None,
        data_branch=None,
        code_changelist=None,
        code_branch=None,
        platform=None,
        package_type=None,
        config=None,
        dest=None,
        region="WW",
    ):
        """
        Fetch Frosty build.
        """
        if dest is None:
            dest = local_paths.get_local_frosty_path()

        source = filer_paths.get_frosty_build_path(
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
            package_type=package_type,
            region=region,
            config=config,
        )
        FilerUtils.fetch_frosty_build_by_source(source, dest)

    @staticmethod
    @collect_metrics()
    def fetch_frosty_build_by_source(
        source: str,
        dest: str = "",
    ):
        """
        Fetch Frosty build by source.
        """
        if not dest:
            dest = local_paths.get_local_frosty_path()

        LOGGER.info("Fetch packaged build from {0} to {1}".format(source, dest))
        core.robocopy(source, dest)

    @staticmethod
    def delete_network_connection(mapped_drive: str = None, network_path: str = None):
        """
        Run Net Use command to remove a single, or all network connections.

        :param mapped_drive: a drive to connect to the resource
        :param network_path: the name of the server and the shared resource
        :return:
        """
        cmd = ["net", "use"]
        if mapped_drive:
            cmd.append(mapped_drive)
        if network_path:
            cmd.append(network_path)
        if not mapped_drive and not network_path:
            cmd.append("*")
        cmd.append("/delete")
        cmd.append("/y")

        core.run(cmd, print_std_out=True)

    @staticmethod
    def auth_network_connection(
        mapped_drive: str = None,
        network_path: str = None,
        username: str = None,
        password: str = None,
    ):
        """
        Run Net Use command to connect a network share using specified credentials
        :param mapped_drive: a drive to connect to the network share
        :param network_path: the name of the network share
        :param username: specifies a different username with which the connection is made
        :param password: specifies the password needed to access the network share
        :return:
        """
        if not network_path:
            raise ELIPYException(
                "Network path is not defined for authentication"
                "Ensure 'build_share' is set in ELIPY config file"
            )

        cmd = ["net", "use"]
        if mapped_drive:
            cmd.append(mapped_drive)
        cmd.append(network_path)

        _username = username
        _password = password

        if not (_username and _password):
            secrets_where = "project_secrets"
            credentials = secrets.get_secrets({secrets_where: True})
            if credentials:
                try:
                    credentials = next(v for v in credentials.values())
                    if credentials["filer_username"] and credentials["filer_password"]:
                        _username = credentials["filer_username"]
                        _password = credentials["filer_password"]
                except KeyError as exc:
                    raise ELIPYException("Failed getting correct credentials") from exc

        if _username and _password:
            cmd.append("%FILER_PASSWORD%")
            cmd.append("/USER:%FILER_USERNAME%")

        core.run(
            cmd,
            print_std_out=True,
            env_patch={"FILER_USERNAME": _username, "FILER_PASSWORD": _password},
        )
