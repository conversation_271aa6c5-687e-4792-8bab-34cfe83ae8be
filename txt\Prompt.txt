CH1-content-dev-first-patch.patchfrosty.bfdata.win64.combine.ww.final.24580360.24580360 job still got this error:
10:51:21 2025-07-25 09:51:20 elipy2 [INFO]: Using pre-created delta bundles from network share
10:51:21 
2025-07-25 09:51:20 elipy2 [ERROR]: Source does not exist, cannot run Robocopy: \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch\24580360\CH1-content-dev\24580360\win64\combined_bundles\CH1-content-dev\24580360\CH1-content-dev-first-patch\24580360\CH1-SP-content-dev\24580321\CH1-SP-content-dev-first-patch\24580321\delta

Before all the changes we have delta directory in here
\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch\24480707\CH1-content-dev\24480707\ps5\combine_bundles\delta
with this command
Get-ChildItem -Path "\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch\" -Recurse -Directory -ErrorAction SilentlyContinue | Where-Object { $_.Name -like "delta" }



We need to produce the delta directory so the job CH1-content-dev-first-patch.patchfrosty.bfdata could find it and fetch

I think the upstream job miss the part of creating that delta folder

Please investigate logic of creating and fetching delta bundles from dst-ci-configuration and elipy
Use C:\Users\<USER>\vscode\pycharm\dst-ci-configuration\src\com\ea\project\bctch1\branchsettings\CH1_SP_content_dev_first_patch.groovy to navigate with logic flow 

