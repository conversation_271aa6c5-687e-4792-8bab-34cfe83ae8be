<?xml version="1.0" encoding="UTF-8"?><project>
    <actions/>
    <description>Code preflight for linux64server in releaseon kin-release.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.plugins.throttleconcurrents.ThrottleJobProperty>
            <maxConcurrentPerNode>1</maxConcurrentPerNode>
            <maxConcurrentTotal>0</maxConcurrentTotal>
            <throttleEnabled>true</throttleEnabled>
            <throttleOption>project</throttleOption>
            <categories/>
        </hudson.plugins.throttleconcurrents.ThrottleJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies code changelist to sync.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>unshelve_changelist</name>
                    <description>Specifies code changelist to preflight</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.ChoiceParameterDefinition>
                    <choices class="java.util.Arrays$ArrayList">
                        <a class="string-array">
                            <string>false</string>
                            <string>true</string>
                        </a>
                    </choices>
                    <name>clean_local</name>
                    <description>If true, TnT/Local will be deleted at the beginning of the run.</description>
                </hudson.model.ChoiceParameterDefinition>
                <hudson.model.ChoiceParameterDefinition>
                    <choices class="java.util.Arrays$ArrayList">
                        <a class="string-array">
                            <string>not</string>
                            <string>do</string>
                        </a>
                    </choices>
                    <name>only_warm_machine</name>
                    <description>If set to "do", only run code_changelist to warm up machine, not care unshelve_changelist.</description>
                </hudson.model.ChoiceParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <EnvInjectJobProperty>
            <info>
                <scriptContent>@ECHO OFF
DEL /F /Q /S D:\dev\logs\* &gt; nul 2&gt;&amp;1
mkdir D:\dev\logs 2&gt; NUL
START /wait taskkill /f /im python.exe &gt;&gt; D:\dev\logs\initial_p4_code_and_data_revert.log 2&gt;&amp;1
START /wait taskkill /f /im fbenvconfigservice.exe &gt;&gt; D:\dev\logs\initial_p4_code_and_data_revert.log 2&gt;&amp;1
p4.exe -p dice-p4buildedge03-fb.dice.ad.ea.com:2001 -u %USERDOMAIN%\%USERNAME% -c jenkins-%NODE_NAME%-codestream revert -w //... &gt;&gt; D:\dev\logs\initial_p4_code_and_data_revert.log 2&gt;&amp;1
p4.exe -p dice-p4buildedge03-fb.dice.ad.ea.com:2001 -u %USERDOMAIN%\%USERNAME% -c jenkins-%NODE_NAME%-codestream print -m 1 -q D:\dev\TnT\masterconfig.xml &gt; NUL
IF NOT ERRORLEVEL 1 del /s /q /f D:\dev\TnT\Bin\Python\*.pyc &gt;&gt; D:\dev\logs\initial_p4_code_and_data_revert.log 2&gt;&amp;1
p4.exe -p dice-p4buildedge03-fb.dice.ad.ea.com:2001 -u %USERDOMAIN%\%USERNAME% -c jenkins-%NODE_NAME%-codestream clean -m D:\dev\TnT\Bin\Python/...
exit 0</scriptContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
    </properties>
    <canRoam>false</canRoam>
    <disabled>false</disabled>
    <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
    <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
    <triggers/>
    <concurrentBuild>true</concurrentBuild>
    <builders>
        <hudson.tasks.BatchFile>
            <command>tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_kingston.yml &gt;&gt; D:\dev\logs\install-elipy.log 2&gt;&amp;1</command>
        </hudson.tasks.BatchFile>
        <hudson.tasks.BatchFile>
            <command>ipconfig | find /i "IPv4" </command>
        </hudson.tasks.BatchFile>
        <hudson.tasks.BatchFile>
            <command>tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_kingston.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location dice codepreflight dice-p4buildedge03-fb.dice.ad.ea.com:2001 jenkins-%NODE_NAME%-codestream linux64server release %unshelve_changelist% --code-branch kin-release --data-directory kindata --%only_warm_machine%-warmup --user %P4_USER%  --licensee BattlefieldGame --email %monkey_email% --password "%monkey_passwd%"</command>
        </hudson.tasks.BatchFile>
    </builders>
    <publishers>
        <hudson.plugins.parameterizedtrigger.BuildTrigger>
            <configs>
                <hudson.plugins.parameterizedtrigger.BuildTriggerConfig>
                    <projects>maintenance.kin-release.code.postpreflight.linux64server</projects>
                    <condition>FAILED</condition>
                    <triggerWithNoParameters>false</triggerWithNoParameters>
                    <configs>
                        <hudson.plugins.parameterizedtrigger.NodeParameters/>
                        <hudson.plugins.parameterizedtrigger.CurrentBuildParameters/>
                        <hudson.plugins.parameterizedtrigger.PredefinedBuildParameters>
                            <properties>preflight_type=code
platform=linux64server
config=release</properties>
                        </hudson.plugins.parameterizedtrigger.PredefinedBuildParameters>
                    </configs>
                </hudson.plugins.parameterizedtrigger.BuildTriggerConfig>
            </configs>
        </hudson.plugins.parameterizedtrigger.BuildTrigger>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.BatchFile>
                            <command>tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_kingston.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location dice kill_processes &gt; %WORKSPACE%\logs\kill_processes.log 2&gt;&amp;1</command>
                        </hudson.tasks.BatchFile>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.StatusCondition">
                        <worstResult>
                            <ordinal>4</ordinal>
                        </worstResult>
                        <bestResult>
                            <ordinal>2</ordinal>
                        </bestResult>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>logs/*.log</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>logs/*.log</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>TnT/*.log</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>TnT/*.log</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>TnT/FrostyLogFile.txt</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>TnT/FrostyLogFile.txt</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>logs/MSBuild_*.failure.txt</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>logs/MSBuild_*.failure.txt</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>TnT/Local/Frosty/Output/*.pkg.verify.log</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>TnT/Local/Frosty/Output/*.pkg.verify.log</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>kindata/.state/kin-release/debugOutput/Win32/game/glaciermp/levels/mp_abbasid/mp_abbasid_networkregistry_debug.log</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>kindata/.state/kin-release/debugOutput/Win32/game/glaciermp/levels/mp_abbasid/mp_abbasid_networkregistry_debug.log</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>kindata/.state/kin-release/debugOutput/Win32/game/glaciermp/levels/mp_abbasid/_layers_gameplay/gameplay_networkregistry_debug.log</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>kindata/.state/kin-release/debugOutput/Win32/game/glaciermp/levels/mp_abbasid/_layers_gameplay/gameplay_networkregistry_debug.log</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>kindata/.state/kin-release/debugOutput/DedicatedServer/game/glaciermp/levels/mp_abbasid/mp_abbasid_networkregistry_debug.log</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>kindata/.state/kin-release/debugOutput/DedicatedServer/game/glaciermp/levels/mp_abbasid/mp_abbasid_networkregistry_debug.log</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>kindata/.state/kin-release/debugOutput/DedicatedServer/game/glaciermp/levels/mp_abbasid/_layers_gameplay/gameplay_networkregistry_debug.log</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>kindata/.state/kin-release/debugOutput/DedicatedServer/game/glaciermp/levels/mp_abbasid/_layers_gameplay/gameplay_networkregistry_debug.log</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>${TEMP}\ELIPY\jenkins\${JOB_NAME}\${BUILD_ID}\*.json</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>${TEMP}\ELIPY\jenkins\${JOB_NAME}\${BUILD_ID}\*.json</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>kindata/.state/kin-release/reports/indeterminism/**/*</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>kindata/.state/kin-release/reports/indeterminism/**/*</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.BatchFile>
                            <command>tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_kingston.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location dice post_clean --user %USERDOMAIN%\%USERNAME% --code-client jenkins-%NODE_NAME%-codestream --code-port dice-p4buildedge03-fb.dice.ad.ea.com:2001 --data-client jenkins-%NODE_NAME%-kindatastream --data-port p4-tunguska-build01.dice.ad.ea.com:2001 &gt;&gt; D:\dev\logs\postclean_silverback.log 2&gt;&amp;1</command>
                        </hudson.tasks.BatchFile>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.StatusCondition">
                        <worstResult>
                            <ordinal>4</ordinal>
                        </worstResult>
                        <bestResult>
                            <ordinal>2</ordinal>
                        </bestResult>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.BatchFile>
                            <command>tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_kingston.yml &gt;&gt; D:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location dice delete_git_lock_file  &gt; %WORKSPACE%\logs\delete_git_lock_file.log 2&gt;&amp;1</command>
                        </hudson.tasks.BatchFile>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.StatusCondition">
                        <worstResult>
                            <ordinal>4</ordinal>
                        </worstResult>
                        <bestResult>
                            <ordinal>2</ordinal>
                        </bestResult>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
    </publishers>
    <buildWrappers>
        <hudson.plugins.ansicolor.AnsiColorBuildWrapper>
            <colorMapName>xterm</colorMapName>
        </hudson.plugins.ansicolor.AnsiColorBuildWrapper>
        <hudson.plugins.timestamper.TimestamperBuildWrapper/>
        <org.jenkinsci.plugins.buildnamesetter.BuildNameSetter>
            <template>${JOB_NAME}.${ENV, var="unshelve_changelist"}</template>
        </org.jenkinsci.plugins.buildnamesetter.BuildNameSetter>
        <hudson.plugins.build__timeout.BuildTimeoutWrapper>
            <strategy class="hudson.plugins.build_timeout.impl.AbsoluteTimeOutStrategy">
                <timeoutMinutes>120</timeoutMinutes>
            </strategy>
            <operationList>
                <hudson.plugins.build__timeout.operations.FailOperation/>
                <hudson.plugins.build__timeout.operations.WriteDescriptionOperation>
                    <description>Build failed due to timeout after {0} minutes</description>
                </hudson.plugins.build__timeout.operations.WriteDescriptionOperation>
            </operationList>
        </hudson.plugins.build__timeout.BuildTimeoutWrapper>
        <org.jenkinsci.plugins.credentialsbinding.impl.SecretBuildWrapper>
            <bindings>
                <org.jenkinsci.plugins.credentialsbinding.impl.UsernamePasswordMultiBinding>
                    <credentialsId>svc_kin01</credentialsId>
                    <usernameVariable>monkey_email</usernameVariable>
                    <passwordVariable>monkey_passwd</passwordVariable>
                </org.jenkinsci.plugins.credentialsbinding.impl.UsernamePasswordMultiBinding>
                <org.jenkinsci.plugins.credentialsbinding.impl.StringBinding>
                    <variable>VAULT_ONLINE_EXC_PROD_SECRET_ID</variable>
                    <credentialsId>cobra-online-rob-prod-secret-id</credentialsId>
                </org.jenkinsci.plugins.credentialsbinding.impl.StringBinding>
            </bindings>
        </org.jenkinsci.plugins.credentialsbinding.impl.SecretBuildWrapper>
        <com.datapipe.jenkins.vault.VaultBuildWrapper plugin="hashicorp-vault-plugin@371.v884a_4dd60fb_6">
            <configuration>
                <vaultUrl>http://127.0.0.1:8200</vaultUrl>
                <vaultCredentialId>vault-auth-dummy</vaultCredentialId>
                <failIfNotFound>true</failIfNotFound>
                <skipSslVerification>false</skipSslVerification>
                <engineVersion>2</engineVersion>
                <timeout>60</timeout>
            </configuration>
            <vaultSecrets>
                <com.datapipe.jenkins.vault.model.VaultSecret>
                    <path>artifacts/automation/dre-pypi-federated/ro</path>
                    <secretValues>
                        <com.datapipe.jenkins.vault.model.VaultSecretValue>
                            <envVar>AF2_USER</envVar>
                            <isRequired>true</isRequired>
                            <vaultKey>username</vaultKey>
                        </com.datapipe.jenkins.vault.model.VaultSecretValue>
                        <com.datapipe.jenkins.vault.model.VaultSecretValue>
                            <envVar>AF2_TOKEN</envVar>
                            <isRequired>true</isRequired>
                            <vaultKey>reference_token</vaultKey>
                        </com.datapipe.jenkins.vault.model.VaultSecretValue>
                    </secretValues>
                </com.datapipe.jenkins.vault.model.VaultSecret>
                <com.datapipe.jenkins.vault.model.VaultSecret>
                    <path>artifacts/automation/dre-generic-federated/ro</path>
                    <secretValues>
                        <com.datapipe.jenkins.vault.model.VaultSecretValue>
                            <envVar>AF2_GENERIC_USER</envVar>
                            <isRequired>true</isRequired>
                            <vaultKey>username</vaultKey>
                        </com.datapipe.jenkins.vault.model.VaultSecretValue>
                        <com.datapipe.jenkins.vault.model.VaultSecretValue>
                            <envVar>AF2_GENERIC_TOKEN</envVar>
                            <isRequired>true</isRequired>
                            <vaultKey>reference_token</vaultKey>
                        </com.datapipe.jenkins.vault.model.VaultSecretValue>
                    </secretValues>
                </com.datapipe.jenkins.vault.model.VaultSecret>
            </vaultSecrets>
            <valuesToMask/>
        </com.datapipe.jenkins.vault.VaultBuildWrapper>
    </buildWrappers>
    <assignedNode>kin-release &amp;&amp; code &amp;&amp; linux64server &amp;&amp; !cloud</assignedNode>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
    <customWorkspace>D:\dev</customWorkspace>
    <scm class="org.jenkinsci.plugins.multiplescms.MultiSCM">
        <scms>
            <scm plugin="p4@1.17.1" class="org.jenkinsci.plugins.p4.PerforceScm">
                <credential>dice-p4buildedge03-fb</credential>
                <workspace class="org.jenkinsci.plugins.p4.workspace.StreamWorkspaceImpl">
                    <charset>none</charset>
                    <pinHost>true</pinHost>
                    <cleanup>false</cleanup>
                    <streamName>//dicestudio/kin/release/kin-release</streamName>
                    <streamAtChange/>
                    <format>jenkins-${NODE_NAME}-codestream</format>
                </workspace>
                <filter/>
                <populate class="org.jenkinsci.plugins.p4.populate.SyncOnlyImpl">
                    <have>true</have>
                    <force>false</force>
                    <modtime>false</modtime>
                    <quiet>true</quiet>
                    <pin>${code_changelist}</pin>
                    <parallel>
                        <enable>true</enable>
                        <path/>
                        <threads>8</threads>
                        <minfiles>0</minfiles>
                        <minbytes>0</minbytes>
                    </parallel>
                    <revert>true</revert>
                </populate>
                <browser class="org.jenkinsci.plugins.p4.browsers.SwarmBrowser">
                    <url>https://swarm.frostbite.com/</url>
                </browser>
            </scm>
            <scm plugin="p4@1.17.1" class="org.jenkinsci.plugins.p4.PerforceScm">
                <credential>dice-p4buildedge03-fb</credential>
                <workspace class="org.jenkinsci.plugins.p4.workspace.StreamWorkspaceImpl">
                    <charset>none</charset>
                    <pinHost>true</pinHost>
                    <cleanup>false</cleanup>
                    <streamName>//dicestudio/kin/release/kin-release</streamName>
                    <streamAtChange/>
                    <format>jenkins-${NODE_NAME}-codestream</format>
                </workspace>
                <filter/>
                <populate class="org.jenkinsci.plugins.p4.populate.SyncOnlyImpl">
                    <have>true</have>
                    <force>false</force>
                    <modtime>false</modtime>
                    <quiet>true</quiet>
                    <pin>${code_changelist}</pin>
                    <parallel>
                        <enable>true</enable>
                        <path/>
                        <threads>8</threads>
                        <minfiles>0</minfiles>
                        <minbytes>0</minbytes>
                    </parallel>
                    <revert>true</revert>
                </populate>
                <browser class="org.jenkinsci.plugins.p4.browsers.SwarmBrowser">
                    <url>https://swarm.frostbite.com/</url>
                </browser>
            </scm>
        </scms>
    </scm>
</project>