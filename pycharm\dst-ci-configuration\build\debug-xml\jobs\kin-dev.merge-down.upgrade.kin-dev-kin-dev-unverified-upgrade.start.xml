<?xml version="1.0" encoding="UTF-8"?><flow-definition>
    <actions/>
    <description>Trigger integrations to task branches using verified changelists.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <org.jenkinsci.plugins.workflow.job.properties.DisableConcurrentBuildsJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.DisableResumeJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495"/>
        <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty plugin="workflow-job@1505.vea_4b_20a_4a_495">
            <triggers>
                <hudson.triggers.SCMTrigger>
                    <spec>H/5 * * * 1-6
H/5 6-23 * * 7</spec>
                    <ignorePostCommitHooks>false</ignorePostCommitHooks>
                </hudson.triggers.SCMTrigger>
            </triggers>
        </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies code changelist to integrate.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>data_changelist</name>
                    <description>Specifies data changelist to integrate.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.ChoiceParameterDefinition>
                    <choices class="java.util.Arrays$ArrayList">
                        <a class="string-array">
                            <string>False</string>
                            <string>True</string>
                        </a>
                    </choices>
                    <name>clean</name>
                    <description>If True, TnT/Local will be cleaned before run.</description>
                </hudson.model.ChoiceParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>integration_reference_job_code=
integration_reference_job=kin-dev.data.start
retry_limit=0
data_upgrade=true
perforce_trigger=true
preview_folder=dev
preview_project_name=kingston
preview_branch=kin-dev-unverified
source_folder=dev
source_project_name=kingston
source_branch=kin-dev
target_branch=kin-dev-kin-dev-unverified-upgrade
dataset=kindata
p4_code_creds=perforce-p4buildedge02-fb-kingston01
p4_code_root=//dicestudio/kin</propertiesContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
    </properties>
    <triggers/>
    <definition class="org.jenkinsci.plugins.workflow.cps.CpsFlowDefinition">
        <script>package scripts.schedulers.all

import com.ea.lib.LibJenkins
import com.ea.project.GetMasterFile

/**
 * verified_integration_scheduler_upgrade.groovy
 */
pipeline {
    agent { label 'scheduler || executor_agent' }
    options {
        allowBrokenBuildClaiming()
        timestamps()
    }
    stages {
        stage('SCM filer check for perforce') {
            steps {
                script {
                    if (env.integration_reference_job == '') {
                        def source_project = ProjectClass(env.source_project_name)
                        P4PreviewData(source_project, 'stream', env.source_folder, env.source_branch, '', '', [dataset: env.dataset])

                        def data_preview_cl = env.P4_CHANGELIST
                        def inject_map = [
                            'data_preview_cl': data_preview_cl,
                        ]
                        EnvInject(currentBuild, inject_map)
                    }
                    if (env.integration_reference_job_code == '') {
                        def preview_project = ProjectClass(env.preview_project_name)
                        P4PreviewCode(preview_project, 'stream', env.preview_folder, env.preview_branch, '', '', [], [], ['p4_code_creds': env.p4_code_creds, 'p4_code_root': env.p4_code_root,])
                    }
                }
            }
        }
        stage('Trigger integration jobs using latest verified changelists.') {
            steps {
                script {
                    def code_changelist
                    def data_changelist
                    // Get changelists
                    if (env.integration_reference_job_code == '') {
                        code_changelist = params.code_changelist ?: env.P4_CHANGELIST
                    } else {
                        def last_successful_code_build = LibJenkins.getLastStableCodeChangelist(env.integration_reference_job_code)
                        code_changelist = params.code_changelist ?: last_successful_code_build
                    }
                    if (env.integration_reference_job == '') {
                        data_changelist = params.data_changelist ?: env.data_preview_cl
                    } else {
                        def last_successful_data_build = LibJenkins.getLastStableDataChangelist(env.integration_reference_job)
                        data_changelist = params.data_changelist ?: last_successful_data_build
                    }
                    def clean = params.clean ?: 'false'

                    // Get data changelist for the previous build of this job, needed to generate the submit message.
                    def last_data_changelist = LibJenkins.getLastStableDataChangelist(env.JOB_NAME) ?: '0'

                    // Get changelist for the previous build of this job, needed to generate the submit message.
                    // This is used for the integrate_upgrade_one_stream jobs, code_changelist is used for legacy reasons.
                    def last_changelist = LibJenkins.getLastStableCodeChangelist(env.JOB_NAME as String)

                    def args = [
                        string(name: 'code_changelist', value: code_changelist),
                        string(name: 'data_changelist', value: data_changelist),
                        string(name: 'last_data_changelist', value: last_data_changelist),
                        string(name: 'last_changelist', value: last_changelist),
                        string(name: 'clean', value: clean),
                    ]
                    def jobs = [:]

                    def inject_map = [
                        'code_changelist'     : code_changelist,
                        'data_changelist'     : data_changelist,
                        'last_data_changelist': last_data_changelist,
                    ]
                    EnvInject(currentBuild, inject_map)
                    currentBuild.displayName = env.JOB_NAME + '.' + data_changelist + '.' + code_changelist

                    // Get branches of different types
                    def masterSettings = GetMasterFile.get_masterfile(BUILD_URL)[0]
                    def integrate_branches = masterSettings.integrate_branches

                    def upgrade_data_branches = []
                    def integrateUpgradeOneStream = []

                    for (branch in integrate_branches.keySet()) {
                        if (integrate_branches[branch].verified_integration
                            &amp;&amp; integrate_branches[branch].data_upgrade
                            &amp;&amp; integrate_branches[branch].source_branch == env.source_branch
                            &amp;&amp; integrate_branches[branch].target_branch == env.target_branch
                            &amp;&amp; integrate_branches[branch].preview_branch == env.preview_branch
                        ) {
                            if (integrate_branches[branch].integrate_upgrade_one_stream) {
                                def branchInfo = integrate_branches[branch]
                                integrateUpgradeOneStream += branchInfo
                            } else {
                                def branchInfo = integrate_branches[branch]
                                upgrade_data_branches += branchInfo
                            }
                        }
                    }

                    // Create and run integration jobs
                    def final_result = Result.SUCCESS
                    def slack_settings = ''
                    for (def run = 0; run &lt;= env.retry_limit.toInteger(); run++) {
                        // Retry failed jobs if retry_limit &gt; 0
                        jobs = [:]
                        final_result = Result.SUCCESS
                        for (branch in upgrade_data_branches) {
                            def data_job_name = branch.source_branch + '.data.' + IntegrationDirection(branch) + '.upgrade.' + branch.target_branch
                            slack_settings = branch.slack_channel ?: slack_settings
                            jobs[data_job_name] = {
                                def downstream_job = build(job: data_job_name, parameters: args, propagate: false)
                                final_result = final_result.combine(Result.fromString(downstream_job.result))
                                LibJenkins.printRunningJobs(this)
                            }
                        }
                        for (branch in integrateUpgradeOneStream) {
                            String integrateUpgradeOneStreamJob = branch.source_branch + '.integrate-upgrade-to.' + branch.target_branch
                            if (NeedsRebuildData(integrateUpgradeOneStreamJob, code_changelist, data_changelist)) {
                                jobs[integrateUpgradeOneStreamJob] = {
                                    def downstreamJob = build(job: integrateUpgradeOneStreamJob, parameters: args, propagate: false)
                                    final_result = final_result.combine(Result.fromString(downstreamJob.result))
                                    LibJenkins.printRunningJobs(this)
                                }
                            }
                        }
                        parallel(jobs)
                        if (final_result == Result.SUCCESS) {
                            break
                        }
                    }
                    currentBuild.result = final_result.toString()

                    /*
                    the job above would not triggere multiple downstream jobs, due to it need to match
                    verified_integration==true &amp;&amp; data_upgrade==true &amp;&amp; preview_branch == env.preview_branch
                    at the same time, so we should be safe to get only one slack channel as return*/
                    if (slack_settings != '') {
                        SlackMessageNew(currentBuild, slack_settings, ProjectClass(env.source_project_name).short_name)
                    }

                    DownstreamErrorReporting(currentBuild)
                }
            }
        }
    }
}
</script>
        <sandbox>true</sandbox>
    </definition>
    <disabled>false</disabled>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
</flow-definition>