<?xml version="1.0" encoding="UTF-8"?><project>
    <actions/>
    <description>Get changelist to use for integration.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Code changelist used for latest successful autotest on CH1-content-dev.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
    </properties>
    <scm class="hudson.scm.NullSCM"/>
    <canRoam>false</canRoam>
    <disabled>false</disabled>
    <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
    <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
    <triggers/>
    <concurrentBuild>false</concurrentBuild>
    <builders/>
    <publishers>
        <hudson.plugins.parameterizedtrigger.BuildTrigger>
            <configs>
                <hudson.plugins.parameterizedtrigger.BuildTriggerConfig>
                    <projects>CH1-content-dev.merge-down.task6.start</projects>
                    <condition>SUCCESS</condition>
                    <triggerWithNoParameters>true</triggerWithNoParameters>
                    <configs class="java.util.Collections$EmptyList"/>
                </hudson.plugins.parameterizedtrigger.BuildTriggerConfig>
            </configs>
        </hudson.plugins.parameterizedtrigger.BuildTrigger>
        <hudson.plugins.parameterizedtrigger.BuildTrigger>
            <configs>
                <hudson.plugins.parameterizedtrigger.BuildTriggerConfig>
                    <projects>CH1-content-dev.merge-down.task8.start</projects>
                    <condition>SUCCESS</condition>
                    <triggerWithNoParameters>true</triggerWithNoParameters>
                    <configs class="java.util.Collections$EmptyList"/>
                </hudson.plugins.parameterizedtrigger.BuildTriggerConfig>
            </configs>
        </hudson.plugins.parameterizedtrigger.BuildTrigger>
        <hudson.plugins.parameterizedtrigger.BuildTrigger>
            <configs>
                <hudson.plugins.parameterizedtrigger.BuildTriggerConfig>
                    <projects>CH1-content-dev.integrate-upgrade-to.CH1-to-trunk.start</projects>
                    <condition>SUCCESS</condition>
                    <triggerWithNoParameters>true</triggerWithNoParameters>
                    <configs class="java.util.Collections$EmptyList"/>
                </hudson.plugins.parameterizedtrigger.BuildTriggerConfig>
            </configs>
        </hudson.plugins.parameterizedtrigger.BuildTrigger>
    </publishers>
    <buildWrappers>
        <hudson.plugins.timestamper.TimestamperBuildWrapper/>
        <org.jenkinsci.plugins.buildnamesetter.BuildNameSetter>
            <template>${JOB_NAME}.${code_changelist}</template>
        </org.jenkinsci.plugins.buildnamesetter.BuildNameSetter>
    </buildWrappers>
    <assignedNode>master</assignedNode>
    <logRotator>
        <daysToKeep>30</daysToKeep>
        <numToKeep>200</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
    <authToken>remotebuild</authToken>
</project>