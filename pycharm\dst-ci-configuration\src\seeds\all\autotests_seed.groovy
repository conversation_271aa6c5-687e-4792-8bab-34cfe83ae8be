package all

import com.ea.CobraLogger
import com.ea.lib.LibCommonCps
import com.ea.lib.LibJobDsl
import com.ea.lib.LibScm
import com.ea.lib.jobs.LibAutotest
import com.ea.lib.jobs.LibAutotestModelBuilder
import com.ea.lib.jobs.LibBilbo
import com.ea.lib.jobs.LibBuildSelector
import com.ea.lib.jobs.LibIntegration
import com.ea.lib.jobs.LibPerforceCounter
import com.ea.lib.model.autotest.AutotestCategory
import com.ea.lib.model.autotest.Platform
import com.ea.lib.model.autotest.jobs.AutotestJobsModel
import com.ea.lib.model.autotest.jobs.AutotestModel
import com.ea.matrixfiles.AutotestMatrix
import com.ea.matrixfiles.AutotestMatrixFactory
import com.ea.project.GetBranchFile
import com.ea.project.GetMasterFile
import com.ea.project.all.All

GetMasterFile.get_masterfile(BUILD_URL).each { masterSettings ->
    def branches = masterSettings.autotest_branches
    branches.each { String currentBranch, info ->
        out.println("   Processing branch: $currentBranch")
        def project = masterSettings.project
        if (All.isAssignableFrom(project)) {
            project = info.project
            out.println "elipyInstallCall: ${project.azure_elipy_install_call}"
            out.println "elipyCall: ${project.azure_elipy_call}"
            out.println "workspaceRoot: ${project.azure_workspace_root}"
        }

        if (info.only_create_views != true) {
            def branchFile = GetBranchFile.get_branchfile(project.name, currentBranch)
            def generalSettings = branchFile.general_settings
            def standardJobsSettings = branchFile.standard_jobs_settings
            def branchInfo = info + generalSettings + standardJobsSettings + [branch_name: currentBranch, project: project]
            def freestyle_jobs = []
            AutotestMatrix autotestMatrix = AutotestMatrixFactory.getInstance(project.autotest_matrix)
            List<AutotestCategory> testCategories = autotestMatrix.getTestCategories(currentBranch)
            List<AutotestCategory> manualTestCategories = autotestMatrix.getManualTestCategories(currentBranch)

            CobraLogger.info('Creating Autotest jobs\n')

            for (testCategory in testCategories) {
                def jobName = LibAutotest.getAutotestStartJobName(
                    currentBranch,
                    testCategory.name
                )
                def autotestStart = pipelineJob(jobName) {
                    properties {
                        pipelineTriggers {
                            triggers {
                                cron {
                                    spec(testCategory.trigger)
                                }
                            }
                        }
                    }
                    definition {
                        cps {
                            script(readFileFromWorkspace('src/scripts/schedulers/all/autotest_scheduler.groovy'))
                            sandbox(true)
                        }
                    }
                }
                LibAutotest.autotestStart(autotestStart, project, branchFile, masterSettings, currentBranch, testCategory)
            }

            if (autotestMatrix.hasManualTestJobs(currentBranch)) {
                def startJobName = LibAutotest.getAutotestStartJobName(
                    currentBranch,
                    'manual'
                )
                def manualAutotestStart = pipelineJob(startJobName) {
                    definition {
                        cps {
                            script(readFileFromWorkspace('src/scripts/schedulers/all/autotest_manual_scheduler.groovy'))
                            sandbox(true)
                        }
                    }
                }
                LibAutotest.autotestManualStart(manualAutotestStart, project, branchFile, masterSettings, currentBranch)
            }

            def buildSelector = job(currentBranch + '.build.selector') {}
            freestyle_jobs.add(buildSelector)
            LibBuildSelector.selectBuild(buildSelector, project, branchFile, masterSettings, currentBranch)
            LibJobDsl.initialP4revert(buildSelector, project, branchInfo, true, false)
            LibJobDsl.addVaultSecrets(buildSelector, branchInfo)
            LibJobDsl.archive_non_build_logs(buildSelector, branchInfo)
            LibJobDsl.postclean_silverback(buildSelector, project, branchInfo)

            List<AutotestCategory> allCategories = new ArrayList<AutotestCategory>(testCategories)
            if (autotestMatrix.hasManualTestJobs(currentBranch)) {
                allCategories.addAll(manualTestCategories)
            }

            for (testCategory in allCategories) {
                List<Platform> defaultPlatforms = autotestMatrix.getPlatforms(currentBranch)
                boolean defaultShouldLevelsRunInParallel = autotestMatrix.shouldLevelsRunInParallel(currentBranch)
                AutotestJobsModel autotestJobs = LibAutotestModelBuilder.composeJobs(testCategory, defaultPlatforms, defaultShouldLevelsRunInParallel, currentBranch, out)
                CobraLogger.info(autotestJobs.modelLog)
                List<AutotestModel> autotestJobsList = autotestJobs.autotestJobs
                def jobRemoteLabel = testCategory.testInfo.remoteLabel ?: testCategory.remoteLabel
                def branchInfoAutotest = branchInfo
                def autotest_location = jobRemoteLabel ?: 'dice'
                def autotest_p4_code_creds = testCategory.p4CodeCreds ?: branchInfo.autotest_remote_settings?.get(autotest_location)?.p4_code_creds ?: project.p4_code_creds
                def autotest_p4_code_server = testCategory.p4CodeServer ?: branchInfo.autotest_remote_settings?.get(autotest_location)?.p4_code_server ?: project.p4_code_server
                def autotest_p4_data_creds = testCategory.p4DataCreds ?: branchInfo.autotest_remote_settings?.get(autotest_location)?.p4_data_creds ?: project.p4_data_creds
                def autotest_p4_data_server = testCategory.p4DataServer ?: branchInfo.autotest_remote_settings?.get(autotest_location)?.p4_data_server ?: project.p4_data_server
                branchInfoAutotest += [
                    p4_code_creds : autotest_p4_code_creds,
                    p4_code_server: autotest_p4_code_server,
                    p4_data_creds : autotest_p4_data_creds,
                    p4_data_server: autotest_p4_data_server,
                ]
                autotestJobsList.each { autotestJob ->
                    def icepickRun = job(autotestJob.name) {
                        description(autotestJob.jobDescription)
                    }
                    freestyle_jobs.add(icepickRun)
                    LibAutotest.autotestRun(icepickRun, project, branchFile, masterSettings, currentBranch, autotestJob, testCategory)
                    LibScm.sync_code_and_data(icepickRun, project, branchInfoAutotest, '${data_changelist}', '${code_changelist}')
                    LibJobDsl.initialP4revert(icepickRun, project, branchInfoAutotest, true, true, true, null, null, testCategory.isFrostedAutotest)
                    LibJobDsl.addVaultSecrets(icepickRun, branchInfoAutotest)
                    LibJobDsl.archive_non_build_logs(icepickRun, branchInfo)
                    LibJobDsl.postclean_silverback(icepickRun, project, branchInfoAutotest, null, null, testCategory.isFrostedAutotest)
                    LibJobDsl.agent_reboot_on_completion(icepickRun)
                    LibAutotest.cleanIcepickLogs(icepickRun, branchInfoAutotest, testCategory.isFrostedAutotest)
                }
            }

            def bilboDroneJob = job(currentBranch + '.bilbo.register-' + branchInfo.dataset + '-autotestutils') {}
            freestyle_jobs.add(bilboDroneJob)
            LibBilbo.bilbo_drone_autotest_job(bilboDroneJob, project, branchFile, masterSettings, currentBranch)
            if (branchInfo.offsite_drone_builds) {
                LibJobDsl.curl_drone_builds(bilboDroneJob, branchInfo)
            }
            LibJobDsl.initialP4revert(bilboDroneJob, project, branchInfo, true, false)
            LibJobDsl.addVaultSecrets(bilboDroneJob, branchInfo)
            LibJobDsl.archive_non_build_logs(bilboDroneJob, branchInfo)
            LibJobDsl.postclean_silverback(bilboDroneJob, project, branchInfo)

            for (testCategory in testCategories) {
                def enableLkgP4Counters = branchInfo.enable_lkg_p4_counters ?: false
                def enableP4Counters = testCategory.enableP4Counters
                def registerVerifiedForPreflight = testCategory.registerVerifiedForPreflight
                def showTestResults = testCategory.testInfo.showTestResultsOnJenkins != null ?
                    testCategory.testInfo.showTestResultsOnJenkins : testCategory.showTestResultsOnJenkins

                if (enableLkgP4Counters && enableP4Counters && showTestResults) {
                    def testP4CounterUpdater = job(currentBranch + '.' + testCategory.name + '.p4counterupdater') {}
                    freestyle_jobs.add(testP4CounterUpdater)
                    // LibScm.git_elipy_sync(test_p4counterupdater, project)
                    LibPerforceCounter.autotestP4CounterUpdater(testP4CounterUpdater, project, branchFile, masterSettings, currentBranch)
                    LibJobDsl.addVaultSecrets(testP4CounterUpdater, branchInfo)
                    LibJobDsl.archive_non_build_logs(testP4CounterUpdater, branchInfo)
                }
                if (registerVerifiedForPreflight) {
                    def verifiedForPreflight = job(currentBranch + '.' + testCategory.name + '.register.verifiedForPreflight') {}
                    freestyle_jobs.add(verifiedForPreflight)
                    LibBilbo.bilbo_register_verified_for_preflight(verifiedForPreflight, project, branchFile, masterSettings, currentBranch)
                    LibJobDsl.addVaultSecrets(verifiedForPreflight, branchInfo)
                }
            }

            if (branchInfo.set_integration_info != null) {
                def autotestToIntegration = job(currentBranch + '.autotest-to-integration.code') {}
                freestyle_jobs.add(autotestToIntegration)
                LibIntegration.autotest_to_integration_code_set(autotestToIntegration, branchInfo)
                LibJobDsl.addVaultSecrets(autotestToIntegration, branchInfo)
            }
            LibCommonCps.add_downstream_freestyle_job_triggers(freestyle_jobs, currentBranch, branchFile.freestyle_job_trigger_matrix)
        }
    }
}
