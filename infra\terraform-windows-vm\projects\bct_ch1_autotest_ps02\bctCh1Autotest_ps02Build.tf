#*************************************************************
#  Sets up the initial needs to point to our vSphere server
#*************************************************************
# Point to our datacentre
# https://www.terraform.io/docs/providers/vsphere/d/datacenter.html
#
# Notes:
# Datastore Cluster: https://www.terraform.io/docs/providers/vsphere/r/datastore_cluster.html
# Individual Datastores: https://www.terraform.io/docs/providers/vsphere/d/datastore.html
# Resource Pools: https://www.terraform.io/docs/providers/vsphere/d/resource_pool.html
# Networking: https://www.terraform.io/docs/providers/vsphere/d/network.html
# Templates: https://www.terraform.io/docs/providers/vsphere/r/virtual_machine.html
# Folder Managment: https://www.terraform.io/docs/providers/vsphere/r/folder.html
# UUID Usage: https://www.terraform.io/docs/providers/random/r/id.html
# Count Usage: https://www.terraform.io/intro/examples/count.html

# *************************************************************
# Terraform state artifactory backup.
# https://www.terraform.io/docs/backends/types/artifactory.html
# *************************************************************
terraform {
  backend "http" {
  }
}
# *************************************************************
# BCT: bct-x, check CONTRIBUTING.md before editing here.
# Labels bct_statebuild and bct_poolbuild are used for bct when we have
# vms with bct code server and bct data server
# *************************************************************

locals {
  module_settings = {
    # ~~ MIGRATION NODES ~~

    # EXECUTOR NODES
    "bct_ch1_autotest_ps_executors_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-02", vm_count = "2", labels = "ps executor_agent", cpu_core = "5", ram_count = 16384 }

    # LKG NODES
    "bct_ch1_ps02_lkg_checkmate_win64_001"                 = { datastore = "BPS02-A1_DRE-BUILD-VMS-02", vm_count = "4", labels = "ps02 lkg_checkmate win64", cpu_core = "5" }
    "bct_ch1_ps02_lkg_auto_ScratchDisk_win64_001"          = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "4", labels = "ps02 lkg_auto_ScratchDisk win64", cpu_core = "5" }
    "bct_ch1_ps02_lkg_auto_ScratchDisk_ps5_001"            = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "4", labels = "ps02 lkg_auto_ScratchDisk ps5", cpu_core = "5" }
    "bct_ch1_ps02_lkg_auto_ScratchDisk_xbsx_002"           = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "4", labels = "ps02 lkg_auto_ScratchDisk xbsx", cpu_core = "5" }
    "bct_ch1_ps02_lkg_bootanddeploy_win64_002"             = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "2", labels = "ps02 lkg_bootanddeploy win64", cpu_core = "5" }
    "bct_ch1_ps02_lkg_bootanddeploy_ps5_002"               = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "2", labels = "ps02 lkg_bootanddeploy ps5", cpu_core = "5" }
    "bct_ch1_ps02_lkg_bootanddeploy_xbsx_002"              = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "2", labels = "ps02 lkg_bootanddeploy xbsx", cpu_core = "5" }
    "bct_ch1_ps02_lkg_bootanddeploy_ScratchDisk_win64_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "2", labels = "ps02 lkg_bootanddeploy_ScratchDisk win64", cpu_core = "5" }
    "bct_ch1_ps02_lkg_bootanddeploy_ScratchDisk_ps5_001"   = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "2", labels = "ps02 lkg_bootanddeploy_ScratchDisk ps5", cpu_core = "5" }
    "bct_ch1_ps02_lkg_bootanddeploy_ScratchDisk_xbsx_001"  = { datastore = "BPS02-A1_DRE-BUILD-VMS-03", vm_count = "2", labels = "ps02 lkg_bootanddeploy_ScratchDisk xbsx", cpu_core = "5" }

    "bct_ch1_ps02_pool_state_bct_ch1_win64_02"  = { datastore = "BPS02-A3_DRE-BUILD-VMS-02", vm_count = "5", labels = "ps02 amd win64_03 statebuild poolbuild win64" }
    "bct_ch1_ps02_pool_state_bct_ch1_win64_003" = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "7", labels = "ps02 amd win64_01 statebuild poolbuild win64" }
    "bct_ch1_ps02_pool_state_bct_ch1_xbsx_003"  = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "4", labels = "ps02 amd xbsx_02 statebuild poolbuild xbsx" }

    # LKGAUTO NODES
    "bct_ch1_ps02_lkgauto_xbsx_001"  = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "3", labels = "ps02 amd lkg_auto xbsx" }
    "bct_ch1_ps02_lkgauto_ps5_001"   = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "3", labels = "ps02 amd lkg_auto ps5" }
    "bct_ch1_ps02_lkgauto_win64_001" = { datastore = "BPS02-A1_DRE-BUILD-VMS-01", vm_count = "3", labels = "ps02 amd lkg_auto win64" }

    # ultspec test
    "bct_ch1_ps02_ultspec_test_001" = { datastore = "BPS02-A4_DRE-BUILD-VMS-01", vm_count = "3", labels = "ps02 poolbuild win64", cpu_core = "5" }

    # UNITTESTS NODES
    "bct_ch1_unittests_engine_001" = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "1", labels = "unittests_engine tool", cpu_core = "5" }
    "bct_ch1_unittests_win64_001"  = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "1", labels = "unittests win64", cpu_core = "5" }

    # STATEBUILD NODES - moved here to alleviate ps03: COBRA-7693
    "bct_ch1_ps02_pool_state_bct_ch1_xbsx_001" = { datastore = "BPS02-A3_DRE-BUILD-VMS-03", vm_count = "3", labels = "ps02 xbsx_01 statebuild poolbuild xbsx" }

  }
}

module "dynamic_local_module_primary" {
  source   = "../../modules/windows_attach_module_v3.5"
  for_each = local.module_settings

  vsphere_datastore       = each.value.datastore
  vm_count                = each.value.vm_count
  vm_prefix               = try(each.value.vm_prefix, "bct2-")
  jenkins_slave_labels    = each.value.labels
  role                    = try(each.value.role, "https://bct-ch1-autotest-jenkins.cobra.dre.ea.com")
  vsphere_compute_cluster = try(each.value.compute_cluster, "DICE-BUILD")
  ram_count               = try(each.value.ram_count, 65536)
  jenkins_websocket       = try(each.value.jenkins_websocket, "disabled")

  vsphere_template      = var.packer_template
  vsphere_network       = var.bct_network
  vsphere_datacenter    = var.bct_datacenter
  vsphere_folder        = "DICE/dre-terraform-nodes/bct_nodes"
  domain_admin          = var.domain_admin
  domain_admin_password = var.domain_password
  local_admin_user      = var.local_username
  local_admin_password  = var.local_password
  project_dir           = var.project_dir
  project_name          = var.project_name
  commit_sha            = var.commit_sha
  commit_user           = var.commit_user
  commit_url            = var.commit_url
  disk_size             = var.disk_size
  domain_name           = "dice.ad.ea.com"
  domain_ou             = "OU=BuildMonkeys,OU=Computers,OU=Stockholm,OU=Offices,DC=dice,DC=ad,DC=ea,DC=com"
  hardware_version      = var.hardware_version
  local_admin_group     = var.local_admin_group
}

# *************************************************************
#  Setting up the dynamic output needed for downstream pipelines
# *************************************************************
# Notes:
# For Expressions: https://www.terraform.io/docs/language/expressions/for.html
# Key Functions: https://www.terraform.io/docs/language/functions/keys.html
# flatten Function: https://www.terraform.io/docs/language/functions/flatten.html
# Output Values: https://www.terraform.io/docs/language/values/outputs.html
# Local Values: https://www.terraform.io/docs/language/values/locals.html
#
# The solution outputs the same way as previously in output.tf
# example:
# node_name_uuids = [
#  {
#   "id"   = "JF5D"
#   "name" = "245e43"
#   },
#
# *************************************************************
#  Dynamic Output, check CONTRIBUTING.md before editing here.
# *************************************************************
locals {
  nodes_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].nodes : [
        {
          name              = node.name
          id                = node.id
          custom_attributes = node.custom_attributes
        }
      ]
    ]
  ])
  node_name_uuids_output = flatten([
    for mod in keys(local.module_settings) : [
      for node in module.dynamic_local_module_primary[mod].node_name_uuids : [
        {
          name = node.hex
          id   = node.id
        }
      ]
    ]
  ])
}

output "nodes" {
  value = local.nodes_output
}

output "node_name_uuids" {
  value = local.node_name_uuids_output
}
