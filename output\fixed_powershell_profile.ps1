# PowerShell Profile with MCP Server Compatibility
# Fixed to prevent warnings when running in non-interactive contexts

# Only configure PSReadLine in interactive sessions
if ([Environment]::UserInteractive -and $Host.UI.RawUI -and -not ([Environment]::GetCommandLineArgs() -contains '-NonInteractive')) {
    try {
        # Import PSReadLine module
        Import-Module PSReadLine -ErrorAction SilentlyContinue
        
        # Configure PSReadLine options only if module loaded successfully
        if (Get-Module PSReadLine) {
            try {
                Set-PSReadLineOption -PredictionSource History -ErrorAction SilentlyContinue
                Set-PSReadLineOption -PredictionViewStyle ListView -ErrorAction SilentlyContinue
                Set-PSReadLineKeyHandler -Key Tab -Function MenuComplete -ErrorAction SilentlyContinue
                Set-PSReadLineOption -HistorySearchCursorMovesToEnd -ErrorAction SilentlyContinue
            }
            catch {
                # Silently ignore PSReadLine configuration errors in non-interactive contexts
            }
        }
    }
    catch {
        # Silently ignore PSReadLine import errors
    }
}

# ================================
# Git Aliases for PowerShell
# ================================
# Based on https://www.reddit.com/r/PowerShell/comments/8pngj5/git_command_line_aliases/
# Enhanced with additional common Git commands

# Git executable path (adjust if Git is installed elsewhere)
$git = "git"

# Basic Git Functions
function git-status { & $git 'status' $args }
function git-add { & $git 'add' $args }
function git-add-all { & $git 'add' '--all' $args }
function git-commit { & $git 'commit' $args }
function git-commit-message { & $git 'commit' '-m' $args }
function git-commit-all { & $git 'commit' '-a' '-m' $args }
function git-checkout { & $git 'checkout' $args }
function git-checkout-branch { & $git 'checkout' '-b' $args }       
function git-branch { & $git 'branch' $args }
function git-branch-delete { & $git 'branch' '-d' $args }
function git-branch-delete-force { & $git 'branch' '-D' $args }     

# Log and History Functions
function git-log { & $git 'log' $args }
function git-log-oneline { & $git 'log' '--oneline' $args }
function git-log-graph { & $git 'log' '--graph' '--oneline' '--decorate' '--all' $args }
function git-log-pretty { & $git 'log' '--pretty=format:"%h %s (%an, %ar)"' $args }

# Remote Operations
function git-pull { & $git 'pull' $args }
function git-push { & $git 'push' $args }
function git-push-origin { & $git 'push' 'origin' $args }
function git-push-upstream { & $git 'push' '-u' 'origin' $args }    
function git-fetch { & $git 'fetch' $args }
function git-remote { & $git 'remote' $args }

# Diff and Show Functions
function git-diff { & $git 'diff' $args }
function git-diff-staged { & $git 'diff' '--staged' $args }
function git-show { & $git 'show' $args }

# Stash Functions
function git-stash { & $git 'stash' $args }
function git-stash-pop { & $git 'stash' 'pop' $args }
function git-stash-list { & $git 'stash' 'list' $args }

# Reset Functions
function git-reset { & $git 'reset' $args }
function git-reset-hard { & $git 'reset' '--hard' $args }
function git-reset-soft { & $git 'reset' '--soft' $args }

# Merge and Rebase Functions
function git-merge { & $git 'merge' $args }
function git-rebase { & $git 'rebase' $args }
function git-rebase-interactive { & $git 'rebase' '-i' $args }      

# Other Useful Functions
function git-clone { & $git 'clone' $args }
function git-init { & $git 'init' $args }
function git-tag { & $git 'tag' $args }
function git-clean { & $git 'clean' $args }
function git-clean-force { & $git 'clean' '-fd' $args }

# Short Aliases (from Reddit post + additional)
# Note: Some aliases avoided conflicts with PowerShell built-ins    
Set-Alias gst git-status
Set-Alias ga git-add
Set-Alias gaa git-add-all
Set-Alias gcom git-commit                # changed from 'gc' (conflicts with Get-Content)
Set-Alias gcommit git-commit-message     # changed from 'gcm' (conflicts with Get-Command)
Set-Alias gcam git-commit-all
Set-Alias gco git-checkout
Set-Alias gcob git-checkout-branch
Set-Alias gb git-branch
Set-Alias gbd git-branch-delete
Set-Alias gbD git-branch-delete-force

# Log aliases
Set-Alias glo git-log
Set-Alias glog git-log-oneline
Set-Alias glg git-log-graph
Set-Alias glp git-log-pretty

# Remote aliases
Set-Alias gpl git-pull
Set-Alias gpush git-push                 # changed from 'gps' (conflicts with Get-Process)
Set-Alias gpso git-push-origin
Set-Alias gpsu git-push-upstream
Set-Alias gf git-fetch
Set-Alias gr git-remote

# Diff aliases
Set-Alias gd git-diff
Set-Alias gds git-diff-staged
Set-Alias gsh git-show

# Stash aliases
Set-Alias gsta git-stash
Set-Alias gstp git-stash-pop
Set-Alias gstl git-stash-list

# Reset aliases
Set-Alias gres git-reset
Set-Alias grh git-reset-hard
Set-Alias grs git-reset-soft

# Merge and rebase aliases
Set-Alias gmerge git-merge               # changed from 'gm' (conflicts with Get-Member)
Set-Alias grb git-rebase
Set-Alias grbi git-rebase-interactive

# Other aliases
Set-Alias gcl git-clone
Set-Alias ginit git-init                 # changed from 'gi' (conflicts with Get-Item)
Set-Alias gt git-tag
Set-Alias gclean git-clean
Set-Alias gcleanf git-clean-force

# Display Git aliases help
function Show-GitAliases {
    Write-Host "=== Git PowerShell Aliases ===" -ForegroundColor Green
    Write-Host ""
    Write-Host "Basic Commands:" -ForegroundColor Yellow
    Write-Host "  gst     - git status"
    Write-Host "  ga      - git add"
    Write-Host "  gaa     - git add --all"
    Write-Host "  gcom    - git commit"
    Write-Host "  gcommit - git commit -m"
    Write-Host "  gcam    - git commit -a -m"
    Write-Host ""
    Write-Host "Branch Operations:" -ForegroundColor Yellow
    Write-Host "  gco     - git checkout"
    Write-Host "  gcob    - git checkout -b"
    Write-Host "  gb      - git branch"
    Write-Host "  gbd     - git branch -d"
    Write-Host "  gbD     - git branch -D (force delete)"
    Write-Host ""
    Write-Host "Log and History:" -ForegroundColor Yellow
    Write-Host "  glo     - git log"
    Write-Host "  glog    - git log --oneline"
    Write-Host "  glg     - git log --graph --oneline --decorate --all"
    Write-Host "  glp     - git log --pretty"
    Write-Host ""
    Write-Host "Remote Operations:" -ForegroundColor Yellow
    Write-Host "  gpl     - git pull"
    Write-Host "  gpush   - git push"
    Write-Host "  gpso    - git push origin"
    Write-Host "  gpsu    - git push -u origin"
    Write-Host "  gf      - git fetch"
    Write-Host "  gr      - git remote"
    Write-Host ""
    Write-Host "Diff and Show:" -ForegroundColor Yellow
    Write-Host "  gd      - git diff"
    Write-Host "  gds     - git diff --staged"
    Write-Host "  gsh     - git show"
    Write-Host ""
    Write-Host "Stash Operations:" -ForegroundColor Yellow
    Write-Host "  gsta    - git stash"
    Write-Host "  gstp    - git stash pop"
    Write-Host "  gstl    - git stash list"
    Write-Host ""
    Write-Host "Reset Operations:" -ForegroundColor Yellow
    Write-Host "  gres    - git reset"
    Write-Host "  grh     - git reset --hard"
    Write-Host "  grs     - git reset --soft"
    Write-Host ""
    Write-Host "Merge and Rebase:" -ForegroundColor Yellow
    Write-Host "  gmerge  - git merge"
    Write-Host "  grb     - git rebase"
    Write-Host "  grbi    - git rebase -i"
    Write-Host ""
    Write-Host "Other Useful:" -ForegroundColor Yellow
    Write-Host "  gcl     - git clone"
    Write-Host "  ginit   - git init"
    Write-Host "  gt      - git tag"
    Write-Host "  gclean  - git clean"
    Write-Host "  gcleanf - git clean -fd"
    Write-Host ""
    Write-Host "Type 'Show-GitAliases' to see this help again." -ForegroundColor Green
}

Set-Alias ghelp Show-GitAliases

# Only show the "Git aliases loaded" message in interactive sessions
if ([Environment]::UserInteractive -and $Host.UI.RawUI) {
    Write-Host "Git aliases loaded! Type 'ghelp' for a list of available aliases." -ForegroundColor Green
}

function login {
    param(
        [Parameter(Mandatory=$true, Position=0)]
        [string]$ComputerName,
        [Parameter(Mandatory=$false)]
        [string]$Username,
        [Parameter(Mandatory=$false)]
        [switch]$Authentication,
        [Parameter(Mandatory=$false)]
        [string]$CredentialsFile = "$env:USERPROFILE\credentials.txt"
    )
    
    # Default username
    $defaultUser = "DICE\svc_bct.dre.build.02"
    
    # Try to resolve the full computer name
    try {
        Write-Host "Resolving computer name: $ComputerName" -ForegroundColor Yellow
        
        # First try to resolve using nslookup
        $resolvedName = $null
        try {
            $nslookupResult = nslookup $ComputerName 2>$null
            if ($nslookupResult) {
                # Extract the full name from nslookup output        
                $nameLines = $nslookupResult | Where-Object { $_ -like "Name:*" }
                if ($nameLines) {
                    $resolvedName = ($nameLines[0] -split "Name:\s+")[1].Trim()
                }
            }
        }
        catch {
            Write-Host "nslookup failed, trying alternative methods..." -ForegroundColor Yellow
        }
        
        # If nslookup didn't work, try Test-NetConnection
        if (-not $resolvedName) {
            try {
                $testResult = Test-NetConnection -ComputerName $ComputerName -InformationLevel Quiet
                if ($testResult) {
                    $resolvedName = $ComputerName
                }
            }
            catch {
                Write-Host "Test-NetConnection failed" -ForegroundColor Yellow
            }
        }
        
        # If still no resolution, try adding common domain suffix   
        if (-not $resolvedName) {
            $possibleNames = @(
                "$ComputerName.dice.ad.ea.com",
                "$ComputerName.ea.com",
                "$ComputerName.local"
            )
            
            foreach ($name in $possibleNames) {
                try {
                    $testResult = Test-NetConnection -ComputerName $name -InformationLevel Quiet -WarningAction SilentlyContinue
                    if ($testResult) {
                        $resolvedName = $name
                        break
                    }
                }
                catch {
                    continue
                }
            }
        }
        
        # Use the resolved name or fall back to original
        $fullComputerName = if ($resolvedName) { $resolvedName } else { $ComputerName }
        Write-Host "Using computer name: $fullComputerName" -ForegroundColor Green
    }
    catch {
        Write-Host "Could not resolve computer name, using original: $ComputerName" -ForegroundColor Yellow
        $fullComputerName = $ComputerName
    }
    
    # Determine which username to use
    $userToUse = if ($Username) { $Username } else { $defaultUser }
    
    # Read credentials from file
    try {
        if (-not (Test-Path $CredentialsFile)) {
            Write-Host "Credentials file not found: $CredentialsFile" -ForegroundColor Red
            Write-Host "Please create a credentials file with format: username password" -ForegroundColor Red
            return
        }
        
        $credentialLines = Get-Content $CredentialsFile
        $userCredentials = @{}
        
        foreach ($line in $credentialLines) {
            if ($line.Trim() -and -not $line.StartsWith("#")) {
                $parts = $line.Split(" ", 2)
                if ($parts.Length -eq 2) {
                    $userCredentials[$parts[0]] = $parts[1]
                }
            }
        }
        
        # Find password for the user
        $password = $null
        if ($userCredentials.ContainsKey($userToUse)) {
            $password = $userCredentials[$userToUse]
        }
        elseif ($userCredentials.ContainsKey($defaultUser)) {
            $password = $userCredentials[$defaultUser]
            if ($Username) {
                Write-Host "Warning: No password found for user '$Username', using default user '$defaultUser'" -ForegroundColor Yellow
                $userToUse = $defaultUser
            }
        }
        else {
            Write-Host "Error: No password found for user '$userToUse' in credentials file" -ForegroundColor Red
            return
        }
        
        # Create secure credential object
        $securePassword = ConvertTo-SecureString $password -AsPlainText -Force
        $credential = New-Object System.Management.Automation.PSCredential($userToUse, $securePassword)
        
        # Build the command parameters
        $sessionParams = @{
            ComputerName = $fullComputerName
            Credential = $credential
        }
        
        # Add authentication if specified
        if ($Authentication) {
            $sessionParams.Authentication = "Credssp"
            Write-Host "Using CredSSP authentication" -ForegroundColor Green
        }
        
        # Display the command that will be executed
        $authString = if ($Authentication) { " -Authentication Credssp" } else { "" }
        Write-Host "Executing: Enter-PSSession -ComputerName $fullComputerName -Credential $userToUse$authString" -ForegroundColor Cyan
        
        # Execute the remote session
        Enter-PSSession @sessionParams
    }
    catch {
        Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Create alias for shorter usage
Set-Alias -Name rlogin -Value login

# Export the function (only when running as module)
if ($MyInvocation.MyCommand.Path -like "*.psm1") {
    Export-ModuleMember -Function login -Alias rlogin
}

# Set aliases with error handling
try {
    Set-Alias -Name grep -Value "C:\Program Files\Git\usr\bin\grep.exe" -ErrorAction SilentlyContinue
    # Fix npm and npx paths
    Set-Alias -Name npm -Value "C:\\Program Files\\nodejs\\npm.cmd" -ErrorAction SilentlyContinue
    Set-Alias -Name npx -Value "C:\\Program Files\\nodejs\\npx.cmd" -ErrorAction SilentlyContinue
}
catch {
    # Silently ignore alias creation errors
}
