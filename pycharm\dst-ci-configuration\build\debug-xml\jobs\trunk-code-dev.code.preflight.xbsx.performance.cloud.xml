<?xml version="1.0" encoding="UTF-8"?><project>
    <actions/>
    <description>Code preflight for xbsx in performanceon trunk-code-dev.</description>
    <keepDependencies>false</keepDependencies>
    <properties>
        <hudson.plugins.throttleconcurrents.ThrottleJobProperty>
            <maxConcurrentPerNode>1</maxConcurrentPerNode>
            <maxConcurrentTotal>0</maxConcurrentTotal>
            <throttleEnabled>true</throttleEnabled>
            <throttleOption>project</throttleOption>
            <categories/>
        </hudson.plugins.throttleconcurrents.ThrottleJobProperty>
        <hudson.model.ParametersDefinitionProperty>
            <parameterDefinitions>
                <hudson.model.StringParameterDefinition>
                    <name>code_changelist</name>
                    <description>Specifies code changelist to sync.</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.StringParameterDefinition>
                    <name>unshelve_changelist</name>
                    <description>Specifies code changelist to preflight</description>
                    <trim>true</trim>
                </hudson.model.StringParameterDefinition>
                <hudson.model.ChoiceParameterDefinition>
                    <choices class="java.util.Arrays$ArrayList">
                        <a class="string-array">
                            <string>false</string>
                            <string>true</string>
                        </a>
                    </choices>
                    <name>clean_local</name>
                    <description>If true, TnT/Local will be deleted at the beginning of the run.</description>
                </hudson.model.ChoiceParameterDefinition>
                <hudson.model.ChoiceParameterDefinition>
                    <choices class="java.util.Arrays$ArrayList">
                        <a class="string-array">
                            <string>not</string>
                            <string>do</string>
                        </a>
                    </choices>
                    <name>only_warm_machine</name>
                    <description>If set to "do", only run code_changelist to warm up machine, not care unshelve_changelist.</description>
                </hudson.model.ChoiceParameterDefinition>
            </parameterDefinitions>
        </hudson.model.ParametersDefinitionProperty>
        <EnvInjectJobProperty>
            <info>
                <propertiesContent>ASAN_WIN_CONTINUE_ON_INTERCEPTION_FAILURE=1</propertiesContent>
                <scriptContent>@ECHO OFF
DEL /F /Q /S E:\dev\logs\* &gt; nul 2&gt;&amp;1
mkdir E:\dev\logs 2&gt; NUL
START /wait taskkill /f /im python.exe &gt;&gt; E:\dev\logs\initial_p4_code_and_data_revert.log 2&gt;&amp;1
START /wait taskkill /f /im fbenvconfigservice.exe &gt;&gt; E:\dev\logs\initial_p4_code_and_data_revert.log 2&gt;&amp;1
p4.exe -p ssl:euwest-p4edge-fb.p4one.ea.com:2001 -u %FROSTED_P4_DOMAINUSER% -c jenkins-%NODE_NAME%-codestream revert -w //... &gt;&gt; E:\dev\logs\initial_p4_code_and_data_revert.log 2&gt;&amp;1
p4.exe -p ssl:euwest-p4edge-fb.p4one.ea.com:2001 -u %FROSTED_P4_DOMAINUSER% -c jenkins-%NODE_NAME%-codestream print -m 1 -q E:\dev\TnT\masterconfig.xml &gt; NUL
IF NOT ERRORLEVEL 1 del /s /q /f E:\dev\TnT\Bin\Python\*.pyc &gt;&gt; E:\dev\logs\initial_p4_code_and_data_revert.log 2&gt;&amp;1
p4.exe -p ssl:euwest-p4edge-fb.p4one.ea.com:2001 -u %FROSTED_P4_DOMAINUSER% -c jenkins-%NODE_NAME%-codestream clean -m E:\dev\TnT\Bin\Python/...
exit 0</scriptContent>
                <loadFilesFromMaster>false</loadFilesFromMaster>
            </info>
            <on>true</on>
            <keepJenkinsSystemVariables>true</keepJenkinsSystemVariables>
            <keepBuildVariables>true</keepBuildVariables>
            <overrideBuildParameters>false</overrideBuildParameters>
            <contributors/>
        </EnvInjectJobProperty>
    </properties>
    <canRoam>false</canRoam>
    <disabled>false</disabled>
    <blockBuildWhenDownstreamBuilding>false</blockBuildWhenDownstreamBuilding>
    <blockBuildWhenUpstreamBuilding>false</blockBuildWhenUpstreamBuilding>
    <triggers/>
    <concurrentBuild>true</concurrentBuild>
    <builders>
        <hudson.tasks.BatchFile>
            <command>tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\install-elipy.bat elipy_bct.yml &gt;&gt; E:\dev\logs\install-elipy.log 2&gt;&amp;1</command>
        </hudson.tasks.BatchFile>
        <hudson.tasks.BatchFile>
            <command>ipconfig | find /i "IPv4" </command>
        </hudson.tasks.BatchFile>
        <hudson.tasks.BatchFile>
            <command>tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; E:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm codepreflight ssl:euwest-p4edge-fb.p4one.ea.com:2001 jenkins-%NODE_NAME%-codestream xbsx performance %unshelve_changelist% --code-branch trunk-code-dev --data-directory bfdata --%only_warm_machine%-warmup --user %P4_USER% --framework-args -D:ondemandp4proxymapfile=E:\dev\tnt\build\framework\data\P4ProtocolOptimalProxyMap.xml --framework-args -D:eaconfig.optimization.ltcg=off  --licensee BattlefieldGame --email %monkey_email% --password "%monkey_passwd%" --framework-args -G:P4ProxyMap.UseServerTag=buildfarm --framework-args -backupgeneratedfiles</command>
        </hudson.tasks.BatchFile>
    </builders>
    <publishers>
        <org.jenkinsci.plugins.postbuildscript.PostBuildScript plugin="postbuildscript@3.4.1-695.vf6b_0b_8053979">
            <config>
                <scriptFiles/>
                <groovyScripts/>
                <buildSteps>
                    <org.jenkinsci.plugins.postbuildscript.model.PostBuildStep>
                        <results>
                            <string>FAILURE</string>
                        </results>
                        <role>BOTH</role>
                        <buildSteps/>
                        <stopOnFailure>false</stopOnFailure>
                    </org.jenkinsci.plugins.postbuildscript.model.PostBuildStep>
                </buildSteps>
                <markBuildUnstable>false</markBuildUnstable>
            </config>
        </org.jenkinsci.plugins.postbuildscript.PostBuildScript>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.BatchFile>
                            <command>tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; E:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm kill_processes &gt; %WORKSPACE%\logs\kill_processes.log 2&gt;&amp;1</command>
                        </hudson.tasks.BatchFile>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.StatusCondition">
                        <worstResult>
                            <ordinal>4</ordinal>
                        </worstResult>
                        <bestResult>
                            <ordinal>2</ordinal>
                        </bestResult>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>logs/*.log</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>logs/*.log</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>TnT/*.log</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>TnT/*.log</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>TnT/FrostyLogFile.txt</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>TnT/FrostyLogFile.txt</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>logs/MSBuild_*.failure.txt</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>logs/MSBuild_*.failure.txt</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>TnT/Local/Frosty/Output/*.pkg.verify.log</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>TnT/Local/Frosty/Output/*.pkg.verify.log</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>bfdata/.state/trunk-code-dev/debugOutput/Win32/game/glaciermp/levels/mp_abbasid/mp_abbasid_networkregistry_debug.log</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>bfdata/.state/trunk-code-dev/debugOutput/Win32/game/glaciermp/levels/mp_abbasid/mp_abbasid_networkregistry_debug.log</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>bfdata/.state/trunk-code-dev/debugOutput/Win32/game/glaciermp/levels/mp_abbasid/_layers_gameplay/gameplay_networkregistry_debug.log</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>bfdata/.state/trunk-code-dev/debugOutput/Win32/game/glaciermp/levels/mp_abbasid/_layers_gameplay/gameplay_networkregistry_debug.log</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>bfdata/.state/trunk-code-dev/debugOutput/DedicatedServer/game/glaciermp/levels/mp_abbasid/mp_abbasid_networkregistry_debug.log</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>bfdata/.state/trunk-code-dev/debugOutput/DedicatedServer/game/glaciermp/levels/mp_abbasid/mp_abbasid_networkregistry_debug.log</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>bfdata/.state/trunk-code-dev/debugOutput/DedicatedServer/game/glaciermp/levels/mp_abbasid/_layers_gameplay/gameplay_networkregistry_debug.log</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>bfdata/.state/trunk-code-dev/debugOutput/DedicatedServer/game/glaciermp/levels/mp_abbasid/_layers_gameplay/gameplay_networkregistry_debug.log</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>${TEMP}\ELIPY\jenkins\${JOB_NAME}\${BUILD_ID}\*.json</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>${TEMP}\ELIPY\jenkins\${JOB_NAME}\${BUILD_ID}\*.json</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.ArtifactArchiver>
                            <artifacts>bfdata/.state/trunk-code-dev/reports/indeterminism/**/*</artifacts>
                            <allowEmptyArchive>true</allowEmptyArchive>
                            <defaultExcludes>true</defaultExcludes>
                            <fingerprint>false</fingerprint>
                            <onlyIfSuccessful>false</onlyIfSuccessful>
                            <followSymlinks>true</followSymlinks>
                            <caseSensitive>true</caseSensitive>
                        </hudson.tasks.ArtifactArchiver>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.FilesMatchCondition">
                        <includes>bfdata/.state/trunk-code-dev/reports/indeterminism/**/*</includes>
                        <excludes/>
                        <baseDir class="org.jenkins_ci.plugins.run_condition.common.BaseDirectory$Workspace"/>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.BatchFile>
                            <command>tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; E:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm post_clean --user %FROSTED_P4_DOMAINUSER% --code-client jenkins-%NODE_NAME%-codestream --code-port ssl:euwest-p4edge-fb.p4one.ea.com:2001 --data-client jenkins-%NODE_NAME%-codestream --data-port dice-p4buildedge02-fb.dice.ad.ea.com:2001 &gt;&gt; E:\dev\logs\postclean_silverback.log 2&gt;&amp;1</command>
                        </hudson.tasks.BatchFile>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.StatusCondition">
                        <worstResult>
                            <ordinal>4</ordinal>
                        </worstResult>
                        <bestResult>
                            <ordinal>2</ordinal>
                        </bestResult>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
        <org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
            <publishers>
                <org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
                    <publisherList>
                        <hudson.tasks.BatchFile>
                            <command>tnt\bin\fbcli\cli.bat x64 &amp;&amp; C:\dev\ci\setup-elipy-env.bat elipy_bct.yml &gt;&gt; E:\dev\logs\setup-elipy-env.log 2&gt;&amp;1 &amp;&amp; elipy --location DiceStockholm delete_git_lock_file --repo-path C:\dev\\ci &gt; %WORKSPACE%\logs\delete_git_lock_file.log 2&gt;&amp;1</command>
                        </hudson.tasks.BatchFile>
                    </publisherList>
                    <runner class="org.jenkins_ci.plugins.run_condition.BuildStepRunner$Fail"/>
                    <condition class="org.jenkins_ci.plugins.run_condition.core.StatusCondition">
                        <worstResult>
                            <ordinal>4</ordinal>
                        </worstResult>
                        <bestResult>
                            <ordinal>2</ordinal>
                        </bestResult>
                    </condition>
                </org.jenkins__ci.plugins.flexible__publish.ConditionalPublisher>
            </publishers>
        </org.jenkins__ci.plugins.flexible__publish.FlexiblePublisher>
    </publishers>
    <buildWrappers>
        <hudson.plugins.ansicolor.AnsiColorBuildWrapper>
            <colorMapName>xterm</colorMapName>
        </hudson.plugins.ansicolor.AnsiColorBuildWrapper>
        <hudson.plugins.timestamper.TimestamperBuildWrapper/>
        <org.jenkinsci.plugins.buildnamesetter.BuildNameSetter>
            <template>${JOB_NAME}.${ENV, var="unshelve_changelist"}</template>
        </org.jenkinsci.plugins.buildnamesetter.BuildNameSetter>
        <hudson.plugins.build__timeout.BuildTimeoutWrapper>
            <strategy class="hudson.plugins.build_timeout.impl.AbsoluteTimeOutStrategy">
                <timeoutMinutes>120</timeoutMinutes>
            </strategy>
            <operationList>
                <hudson.plugins.build__timeout.operations.FailOperation/>
                <hudson.plugins.build__timeout.operations.WriteDescriptionOperation>
                    <description>Build failed due to timeout after {0} minutes</description>
                </hudson.plugins.build__timeout.operations.WriteDescriptionOperation>
            </operationList>
        </hudson.plugins.build__timeout.BuildTimeoutWrapper>
        <org.jenkinsci.plugins.credentialsbinding.impl.SecretBuildWrapper>
            <bindings>
                <org.jenkinsci.plugins.credentialsbinding.impl.UsernamePasswordMultiBinding>
                    <credentialsId>monkey.bct</credentialsId>
                    <usernameVariable>monkey_email</usernameVariable>
                    <passwordVariable>monkey_passwd</passwordVariable>
                </org.jenkinsci.plugins.credentialsbinding.impl.UsernamePasswordMultiBinding>
                <org.jenkinsci.plugins.credentialsbinding.impl.StringBinding>
                    <variable>VAULT_ONLINE_EXC_PROD_SECRET_ID</variable>
                    <credentialsId>cobra-online-rob-prod-secret-id</credentialsId>
                </org.jenkinsci.plugins.credentialsbinding.impl.StringBinding>
            </bindings>
        </org.jenkinsci.plugins.credentialsbinding.impl.SecretBuildWrapper>
        <com.datapipe.jenkins.vault.VaultBuildWrapper plugin="hashicorp-vault-plugin@371.v884a_4dd60fb_6">
            <configuration>
                <vaultUrl>http://127.0.0.1:8200</vaultUrl>
                <vaultCredentialId>vault-auth-dummy</vaultCredentialId>
                <failIfNotFound>true</failIfNotFound>
                <skipSslVerification>false</skipSslVerification>
                <engineVersion>2</engineVersion>
                <timeout>60</timeout>
            </configuration>
            <vaultSecrets>
                <com.datapipe.jenkins.vault.model.VaultSecret>
                    <path>artifacts/automation/dre-pypi-federated/ro</path>
                    <secretValues>
                        <com.datapipe.jenkins.vault.model.VaultSecretValue>
                            <envVar>AF2_USER</envVar>
                            <isRequired>true</isRequired>
                            <vaultKey>username</vaultKey>
                        </com.datapipe.jenkins.vault.model.VaultSecretValue>
                        <com.datapipe.jenkins.vault.model.VaultSecretValue>
                            <envVar>AF2_TOKEN</envVar>
                            <isRequired>true</isRequired>
                            <vaultKey>reference_token</vaultKey>
                        </com.datapipe.jenkins.vault.model.VaultSecretValue>
                    </secretValues>
                </com.datapipe.jenkins.vault.model.VaultSecret>
                <com.datapipe.jenkins.vault.model.VaultSecret>
                    <path>artifacts/automation/dre-generic-federated/ro</path>
                    <secretValues>
                        <com.datapipe.jenkins.vault.model.VaultSecretValue>
                            <envVar>AF2_GENERIC_USER</envVar>
                            <isRequired>true</isRequired>
                            <vaultKey>username</vaultKey>
                        </com.datapipe.jenkins.vault.model.VaultSecretValue>
                        <com.datapipe.jenkins.vault.model.VaultSecretValue>
                            <envVar>AF2_GENERIC_TOKEN</envVar>
                            <isRequired>true</isRequired>
                            <vaultKey>reference_token</vaultKey>
                        </com.datapipe.jenkins.vault.model.VaultSecretValue>
                    </secretValues>
                </com.datapipe.jenkins.vault.model.VaultSecret>
            </vaultSecrets>
            <valuesToMask/>
        </com.datapipe.jenkins.vault.VaultBuildWrapper>
    </buildWrappers>
    <assignedNode>trunk-code-dev &amp;&amp; code &amp;&amp; xbsx &amp;&amp; performance &amp;&amp; cloud</assignedNode>
    <logRotator>
        <daysToKeep>7</daysToKeep>
        <numToKeep>100</numToKeep>
        <artifactDaysToKeep>-1</artifactDaysToKeep>
        <artifactNumToKeep>-1</artifactNumToKeep>
    </logRotator>
    <quietPeriod>0</quietPeriod>
    <customWorkspace>E:\dev</customWorkspace>
    <scm class="org.jenkinsci.plugins.multiplescms.MultiSCM">
        <scms>
            <scm plugin="p4@1.17.1" class="org.jenkinsci.plugins.p4.PerforceScm">
                <credential>euwest-p4edge-fb.p4one.ea.com</credential>
                <workspace class="org.jenkinsci.plugins.p4.workspace.StreamWorkspaceImpl">
                    <charset>none</charset>
                    <pinHost>true</pinHost>
                    <cleanup>false</cleanup>
                    <streamName>//bf/mainline/trunk-code-dev</streamName>
                    <streamAtChange/>
                    <format>jenkins-${NODE_NAME}-codestream</format>
                </workspace>
                <filter/>
                <populate class="org.jenkinsci.plugins.p4.populate.SyncOnlyImpl">
                    <have>true</have>
                    <force>false</force>
                    <modtime>false</modtime>
                    <quiet>true</quiet>
                    <pin>${code_changelist}</pin>
                    <parallel>
                        <enable>true</enable>
                        <path/>
                        <threads>8</threads>
                        <minfiles>0</minfiles>
                        <minbytes>0</minbytes>
                    </parallel>
                    <revert>true</revert>
                </populate>
                <browser class="org.jenkinsci.plugins.p4.browsers.SwarmBrowser">
                    <url>https://swarm.frostbite.com/</url>
                </browser>
            </scm>
            <scm plugin="p4@1.17.1" class="org.jenkinsci.plugins.p4.PerforceScm">
                <credential>euwest-p4edge-fb.p4one.ea.com</credential>
                <workspace class="org.jenkinsci.plugins.p4.workspace.StreamWorkspaceImpl">
                    <charset>none</charset>
                    <pinHost>true</pinHost>
                    <cleanup>false</cleanup>
                    <streamName>//bf/mainline/trunk-code-dev</streamName>
                    <streamAtChange/>
                    <format>jenkins-${NODE_NAME}-codestream</format>
                </workspace>
                <filter/>
                <populate class="org.jenkinsci.plugins.p4.populate.SyncOnlyImpl">
                    <have>true</have>
                    <force>false</force>
                    <modtime>false</modtime>
                    <quiet>true</quiet>
                    <pin>${code_changelist}</pin>
                    <parallel>
                        <enable>true</enable>
                        <path/>
                        <threads>8</threads>
                        <minfiles>0</minfiles>
                        <minbytes>0</minbytes>
                    </parallel>
                    <revert>true</revert>
                </populate>
                <browser class="org.jenkinsci.plugins.p4.browsers.SwarmBrowser">
                    <url>https://swarm.frostbite.com/</url>
                </browser>
            </scm>
        </scms>
    </scm>
</project>