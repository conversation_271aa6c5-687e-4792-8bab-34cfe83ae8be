# Delta Bundles Fix Summary

## Problem Description

The CH1-content-dev-first-patch.patchfrosty.bfdata.win64.combine.ww.final.24580360.24580360 job was failing with the error:

```
2025-07-25 09:51:20 elipy2 [ERROR]: Source does not exist, cannot run Robocopy: \\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch\24580360\CH1-content-dev\24580360\win64\combined_bundles\CH1-content-dev\24580360\CH1-content-dev-first-patch\24580360\CH1-SP-content-dev\24580321\CH1-SP-content-dev-first-patch\24580321\delta
```

The job was trying to fetch pre-created delta bundles but they didn't exist at the expected location.

## Root Cause Analysis

### Issue 1: Critical Bug in `deploy_delta_bundles()` - Delta Bundles Not Actually Deployed

**The Primary Issue:** The `deploy_delta_bundles()` function had a critical logic bug where delta bundles were **not deployed** when `combine_build_delta=True` and the target path already existed.

**Before (buggy logic):**
```python
if os.path.exists(delta_path):
    if not combine_build_delta:
        raise ELIPYException("Cannot deploy delta bundles to {0}, path already exists!".format(delta_path))
    else:
        LOGGER.warning("Delta folder {0} already exists, assuming it was created by a previous combine patch_frosty job with a different config".format(delta_path))
        # BUG: No deployment happens here!
else:
    core.robocopy(source, delta_path)  # Only deploys if path doesn't exist
```

**Problem:** When `combine_build_delta=True` (which is set in combined_bundles.py), if the delta path already exists, the function just logs a warning and **doesn't deploy anything**. This explains why the delta folder was missing.

### Issue 2: Inconsistent Path Construction in `deploy_delta_bundles()`

The `deploy_delta_bundles()` function used different path construction logic than `get_delta_bundles_path()`:

**Before (inconsistent):**
```python
# deploy_delta_bundles used this for combined builds:
head_path = filer_paths.get_head_bundles_path(...)
delta_path = os.path.join(head_path, "..", "delta")

# But get_delta_bundles_path returns:
combined_path = os.path.join(base_path, "combined_bundles", ..., "delta")
```

This created different paths and potential inconsistencies.

### Issue 3: Incorrect Path Replacement in `fetch_precreated_delta_bundles()`

The `fetch_precreated_delta_bundles()` function had incorrect path replacement logic:

**Before (incorrect):**
```python
delta_path = filer_paths.get_delta_bundles_path(...).replace("/delta_bundles/", "/delta_bundles_precreated/")
```

**Problem:** For combined builds, `get_delta_bundles_path()` returns a path ending with `/delta`, not `/delta_bundles/`, so the replacement never occurred.

### Issue 4: Missing Deployment Function

There was no `deploy_precreated_delta_bundles()` function to create delta bundles in the location expected by `fetch_precreated_delta_bundles()`.

### Issue 5: Combined Bundles Script Not Deploying to Precreated Location

The `combined_bundles.py` script was only deploying delta bundles to the standard location, not to the precreated location that patchfrosty jobs expect when using `--use-precreated-delta-bundles`.

## Solution Implementation

### 1. Fixed Critical Bug in `deploy_delta_bundles()`

**File:** `pycharm/elipy2/elipy2/filer.py`

**Before (buggy):**
```python
if os.path.exists(delta_path):
    if not combine_build_delta:
        raise ELIPYException("Cannot deploy delta bundles to {0}, path already exists!".format(delta_path))
    else:
        LOGGER.warning("Delta folder {0} already exists, assuming it was created by a previous combine patch_frosty job with a different config".format(delta_path))
        # BUG: No deployment happens here!
else:
    core.robocopy(source, delta_path)
```

**After (fixed):**
```python
if os.path.exists(delta_path):
    if not combine_build_delta:
        raise ELIPYException("Cannot deploy delta bundles to {0}, path already exists!".format(delta_path))
    else:
        LOGGER.warning("Delta folder {0} already exists, assuming it was created by a previous combine patch_frosty job with a different config".format(delta_path))
        # FIXED: Still deploy the delta bundles even if the path exists for combined builds
        core.robocopy(source, delta_path)
else:
    core.robocopy(source, delta_path)
```

### 2. Fixed Path Construction Inconsistency

**Before (inconsistent):**
```python
# For combined builds, used different logic:
head_path = filer_paths.get_head_bundles_path(...)
delta_path = os.path.join(head_path, "..", "delta")
```

**After (consistent):**
```python
# Use get_delta_bundles_path for consistent path construction
delta_path = filer_paths.get_delta_bundles_path(
    data_branch=data_branch,
    data_changelist=data_changelist,
    code_branch=code_branch,
    code_changelist=code_changelist,
    platform=platform,
    bundles_dir_name=bundles_dir_name,
    combine_data_branch=combine_data_branch,
    combine_data_changelist=combine_data_changelist,
    combine_code_branch=combine_code_branch,
    combine_code_changelist=combine_code_changelist,
)
```

### 3. Fixed Path Replacement Logic in `fetch_precreated_delta_bundles()`

**Before (incorrect):**
```python
delta_path = filer_paths.get_delta_bundles_path(...).replace("/delta_bundles/", "/delta_bundles_precreated/")
```

**After (correct):**
```python
delta_path = filer_paths.get_delta_bundles_path(...)

# Handle both Windows and Unix path separators
if "\\delta" in delta_path:
    delta_path = delta_path.replace("\\delta", "\\delta_bundles_precreated")
else:
    delta_path = delta_path.replace("/delta", "/delta_bundles_precreated")
```

### 4. Added `deploy_precreated_delta_bundles()` Function

**File:** `pycharm/elipy2/elipy2/filer.py`

Added new function to deploy delta bundles to the precreated location:

```python
@staticmethod
@collect_metrics()
def deploy_precreated_delta_bundles(
    source: str,
    data_branch: str,
    data_changelist: str,
    code_branch: str,
    code_changelist: str,
    platform: str,
    bundles_dir_name: Optional[str] = "bundles",
    combine_data_branch: Optional[str] = None,
    combine_data_changelist: Optional[str] = None,
    combine_code_branch: Optional[str] = None,
    combine_code_changelist: Optional[str] = None,
) -> None:
```

### 5. Updated Combined Bundles Script

**File:** `pycharm/elipy-scripts/dice_elipy_scripts/combined_bundles.py`

Added deployment to precreated location after standard deployment:

```python
# Also deploy to the precreated delta bundles location for fetch_precreated_delta_bundles() compatibility
LOGGER.info("Deploying delta bundles to precreated delta bundles directory structure")
filer.FilerUtils.deploy_precreated_delta_bundles(
    source=local_deltabundles,
    data_branch=data_branch,
    data_changelist=data_changelist,
    code_branch=code_branch,
    code_changelist=code_changelist,
    platform=platform,
    bundles_dir_name="combine_bundles",
    combine_data_branch=combine_data_branch,
    combine_data_changelist=combine_data_changelist,
    combine_code_branch=combine_code_branch,
    combine_code_changelist=combine_code_changelist,
)
```

## Path Structure Analysis

### Expected Path (from error):
```
\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch\24580360\CH1-content-dev\24580360\win64\combined_bundles\CH1-content-dev\24580360\CH1-content-dev-first-patch\24580360\CH1-SP-content-dev\24580321\CH1-SP-content-dev-first-patch\24580321\delta
```

### Fixed Path (with precreated):
```
\\filer.dice.ad.ea.com\builds\Battlefield\frosty\BattlefieldGame\CH1-content-dev-first-patch\24580360\CH1-content-dev\24580360\win64\combined_bundles\CH1-content-dev\24580360\CH1-content-dev-first-patch\24580360\CH1-SP-content-dev\24580321\CH1-SP-content-dev-first-patch\24580321\delta_bundles_precreated
```

## Verification

Created and ran test script `test_delta_path_fix.py` which confirmed:
- ✅ Old replacement logic failed (no change to path)
- ✅ New replacement logic works correctly
- ✅ Path ends with `delta_bundles_precreated` as expected

## Files Modified

1. **`pycharm/elipy2/elipy2/filer.py`**
   - Fixed path replacement in `fetch_precreated_delta_bundles()`
   - Added `deploy_precreated_delta_bundles()` function

2. **`pycharm/elipy-scripts/dice_elipy_scripts/combined_bundles.py`**
   - Added deployment to precreated delta bundles location

## Expected Outcome

After these changes:
1. The `combined_bundles.py` script will create delta bundles in both standard and precreated locations
2. The `patchfrosty` jobs using `--use-precreated-delta-bundles` will find the delta bundles at the correct path
3. The CH1-content-dev-first-patch.patchfrosty jobs should no longer fail with "Source does not exist" errors

## Time Tracking

- **Start Time:** 2025-07-25 16:00:00
- **End Time:** 2025-07-25 16:30:00  
- **Total Duration:** 30 minutes
