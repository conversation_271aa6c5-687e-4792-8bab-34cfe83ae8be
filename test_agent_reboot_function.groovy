#!/usr/bin/env groovy

// Simple test script to verify agent_reboot_on_success function syntax
@Grab('org.jenkins-ci.plugins:job-dsl-core:1.77')

import javaposse.jobdsl.dsl.DslFactory
import javaposse.jobdsl.dsl.Job

// Mock job factory to test the function
def mockJobFactory = [
    job: { name ->
        return [
            with: { closure ->
                closure.delegate = [
                    publishers: { publisherClosure ->
                        publisherClosure.delegate = [
                            downstreamParameterized: { downstreamClosure ->
                                downstreamClosure.delegate = [
                                    trigger: { jobName, triggerClosure ->
                                        triggerClosure.delegate = [
                                            condition: { cond -> println "Condition: $cond" },
                                            parameters: { paramClosure ->
                                                paramClosure.delegate = [
                                                    nodeLabel: { key, value, nodeClosure ->
                                                        nodeClosure.delegate = [
                                                            nodeEligibility: { eligibility -> println "Node eligibility: $eligibility" }
                                                        ]
                                                        nodeClosure()
                                                        println "Node label: $key = $value"
                                                    }
                                                ]
                                                paramClosure()
                                            }
                                        ]
                                        triggerClosure()
                                        println "Trigger job: $jobName"
                                    }
                                ]
                                downstreamClosure()
                            }
                        ]
                        publisherClosure()
                    }
                ]
                closure()
            }
        ]
    }
] as DslFactory

// Test the agent_reboot_on_success function
class LibJobDsl {
    static void agent_reboot_on_success(def job) {
        job.with {
            publishers {
                downstreamParameterized {
                    trigger('agent.reboot') {
                        condition('SUCCESS')
                        parameters {
                            nodeLabel('machine', '${NODE_NAME}') {
                                nodeEligibility('IgnoreOfflineNodeEligibility')
                            }
                        }
                    }
                }
            }
        }
    }
}

// Test execution
println "Testing agent_reboot_on_success function..."
def testJob = mockJobFactory.job('test-job')
LibJobDsl.agent_reboot_on_success(testJob)
println "Test completed successfully!"
